import { RoleWithPermissionsAndProperties } from "../general"
import { check } from "./checks/check"

export const cohost: RoleWithPermissionsAndProperties = {
  permissions: {
    community: {},
    userdata: {},
    global: {},
    event: {
      join: false,
      update: true,
      approve: true,
      allowShare: ({ data: event }) => check(event.share !== "none"), // if I can share someones elses games
      shareUserGames: ({ data: event }) => check(event.share !== "none"), // if I can share my games
      isSuper: true,
      isCohost: true,
    },
  },
  properties: {
    subject: "event",
    level: "super",
  },
}
