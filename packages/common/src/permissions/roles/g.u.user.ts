import { RoleWithPermissionsAndProperties } from "../general"
import { getSetting } from "./helpers/getSetting"
import { eventJoin } from "./checks/event.join"
import { check } from "./checks/check"

export const user: RoleWithPermissionsAndProperties = {
  permissions: {
    community: {
      viewGeneral: ({ data: community }) =>
        check(
          community.openness === "publicLimited" ||
            community.openness === "public",
        ),
      view: ({ data: community }) =>
        check(
          community.openness === "publicLimited" ||
            community.openness === "public",
        ),
    },
    userdata: {
      view: ({ user, data }) => check(user.id === data.id),
      update: ({ user, data }) => check(user.id === data.id),
      delete: ({ user, data }) => check(user.id === data.id),
    },
    global: {
      isUser: true,
      createEvent: true,
      createCommunity: ({ data: globalData, role }) => {
        const create = getSetting(
          role?.roleSettings ?? [],
          "create_communities_max",
        )
        if (!create) return null
        if (create === true) return true

        return check(Number(create) > (globalData.createdCommunities ?? 0))
      },
    },
    event: {
      join: eventJoin,
      view: ({ data: event }) =>
        check(
          event.openness === "public" || event.openness === "publicLimited",
        ),
      viewPublic: ({ data: event }) =>
        check(
          event.openness === "public" || event.openness === "publicLimited",
        ),
    },
  },
  properties: {
    subject: "global",
    level: "user",
  },
}
