import { RoleWithPermissionsAndProperties } from "../general"
import { eventJoin } from "./checks/event.join"
import { check } from "./checks/check"

export const host: RoleWithPermissionsAndProperties = {
  permissions: {
    community: {},
    userdata: {},
    global: {},
    event: {
      join: eventJoin,
      isHost: true,
      isSuper: true,
      promoteCohost: true,
      update: true,
      delete: true,
      approve: true,
      allowShare: ({ data: event }) => check(event.share !== "none"), // if I can share someones elses games
      shareUserGames: ({ data: event }) => check(event.share !== "none"), // if I can share my games
    },
  },
  properties: {
    subject: "event",
    level: "super",
  },
}
