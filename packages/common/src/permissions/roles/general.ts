import { RolesWithPermissions } from "../general"
import { trustedmember } from "./c.s.trustedmember"
import { trusted } from "./g.s.trusted"
import { superadmin } from "./g.s.superadmin"
import { owner } from "./c.s.owner"
import { user } from "./g.u.user"
import { member } from "./c.u.member"
import { invited } from "./c.p.invited"
import { moder } from "./c.s.moder"
import { unverified } from "./g.p.unverified"
import { admin } from "./g.s.admin"
import { host } from "./e.s.host"
import { cohost } from "./e.s.cohost"
import { interested } from "./e.p.interested"
import { participant } from "./e.u.participant"
import { requested } from "./e.p.requested"
import { reserved } from "./e.p.reserved"
import { unwelcome } from "./e.b.unwelcome"
import { disgraced } from "./c.b.disgraced"
import { unregistered } from "./g.p.unregistered"

/*
 Role title anatomy:

 [category][type][name]

 category:
 c - community
 e - event
 g - global

 type:
 b - banned - prevents any interaction, can't hold other roles with the same subject*
 p - pending - waiting for approval, can have multiple pending roles, all of them get promoted to user role when approved
 s - super - advanced user role, improves permissions. Can hold only one super role at a time
 u - user - basic user role, can have only one of each type at a time for each subject.
    Must have at least one user role for each subject top be considered a member.
    "s" roles are created assuming that user will have user role for same subject.

    * subject - community with specific id, event with specific id, global - all effects are global

    Global hierarchy:
    superadmin > admin > s role > u role > p role > b role
 */

export const roles = {
  superadmin,
  owner,
  user,
  member,
  invited,
  moder,
  unverified,
  admin,
  trusted,
  trustedmember,
  host,
  cohost,
  interested,
  participant,
  requested,
  reserved,
  unwelcome,
  disgraced,
  unregistered,
} as const satisfies RolesWithPermissions
