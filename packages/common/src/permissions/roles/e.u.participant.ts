import { RoleWithPermissionsAndProperties } from "../general"
import { check } from "./checks/check"

export const participant: RoleWithPermissionsAndProperties = {
  permissions: {
    community: {},
    userdata: {},
    global: {},
    event: {
      join: false,
      isParticipant: true,
      view: true,
      viewPublic: true,
      shareUserGames: ({ data: event }) =>
        check((event.share ?? "all") === "all"), // if I can share my games
    },
  },
  properties: {
    subject: "event",
    level: "user",
  },
}
