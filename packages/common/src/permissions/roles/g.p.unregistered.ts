import { RoleWithPermissionsAndProperties } from "../general"
import { check } from "./checks/check"

export const unregistered: RoleWithPermissionsAndProperties = {
  permissions: {
    community: {
      viewGeneral: ({ data: community }) =>
        check(
          community.openness === "publicLimited" ||
            community.openness === "public",
        ),
    },
    userdata: {},
    global: {},
    event: {
      viewPublic: ({ data: event }) =>
        check(
          event.openness === "public" || event.openness === "publicLimited",
        ),
    },
  },
  properties: {
    subject: "global",
    level: "pending",
  },
}
