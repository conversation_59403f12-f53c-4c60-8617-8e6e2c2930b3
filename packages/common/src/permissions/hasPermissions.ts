import { Permissions } from "./objects/general"
import { RolesWithPermissions } from "./general"
import { roles } from "./roles/general"
import { isRole, Role, RoleData, User } from "./roles/helpers/types"
import { Community } from "./objects/community"
import { Userdata } from "./objects/userdata"
import { Event } from "./objects/event"
import { GlobalData } from "./objects/global"

const logFilterString = null //"event"
const logResultCheck = false
const logTraversingCheck = false

const logFilter = (
  role: string,
  action: string,
  resource: string,
  forceLog: boolean = false,
) =>
  logFilterString || forceLog
    ? role === logFilterString ||
      action === logFilterString ||
      resource === logFilterString
    : true

type LogResultParams = {
  user: User | null
  resource: string
  action: string
  result: boolean | null
  data?: Community | Userdata | GlobalData | Event
  details?: string
  forceLog?: boolean
}

const logResult = ({
  user,
  resource,
  action,
  result,
  data,
  details,
  forceLog = false,
}: LogResultParams) => {
  if ((forceLog || logResultCheck) && logFilter("", action, resource)) {
    console.info(
      result ? "✅" : "❌",
      "## RESULT ##",
      result,
      resource,
      action,
      user,
      data,
      details,
    )
  }
}

type LogTraversingParams = {
  user: User | null
  resource: string
  action: string
  granter: Role
  result: boolean | null
  data?: Community | Userdata | GlobalData | Event
  forceLog?: boolean
}

const logTraversing = ({
  user,
  resource,
  action,
  granter,
  result,
  data,
  forceLog = false,
}: LogTraversingParams) => {
  if (
    (forceLog || logTraversingCheck) &&
    logFilter(granter, action, resource)
  ) {
    console.info(
      result ? "✅" : "❌",
      resource,
      action,
      result,
      user,
      data,
      granter,
    )
  }
}

export function getRoleProps(resource: Role) {
  return roles[resource].properties ?? {}
}

export function hasPermission<Resource extends keyof Permissions>(
  user: User | null,
  resource: Resource,
  action: Permissions[Resource]["action"],
  data?: Permissions[Resource]["dataType"],
  forceLog = false,
) {
  // Collect all permission values from applicable roles
  const permissionValues: (boolean | null)[] = []
  let hasApplicableRole = false

  const useRoles: RoleData[] =
    user && user.roles && user.roles.length > 0
      ? user.roles
      : [
          {
            role: "unregistered",
            subject: "global",
            subjectId: null,
          },
        ]

  useRoles.forEach((role) => {
    if (!isRole(role.role)) return

    if (
      role.subject === resource &&
      role.subjectId !== null &&
      role.subjectId !== data?.id
    ) {
      return
    }

    hasApplicableRole = true

    const permission = (roles as RolesWithPermissions)[role.role].permissions?.[
      resource
    ]?.[action]

    if (!permission && typeof permission !== "boolean") {
      // permissionValues.push(null)
      return
    }

    if (typeof permission === "boolean") {
      permissionValues.push(permission)

      logTraversing({
        user,
        resource,
        action,
        granter: role.role,
        result: permission,
        data,
        forceLog,
      })
      return
    }

    const permissionGained =
      data != null &&
      permission({
        user: user ?? ({} as User),
        data,
        role,
        properties: (roles as RolesWithPermissions)[role.role].properties,
      })

    logTraversing({
      user,
      resource,
      action,
      granter: role.role,
      result: permissionGained,
      data,
      forceLog,
    })

    permissionValues.push(permissionGained)
  })

  const hasFalse = permissionValues.includes(false)
  const hasTrue = permissionValues.includes(true)

  let result: boolean

  if (!hasApplicableRole) {
    result = false
  } else if (hasFalse) {
    result = false
  } else if (hasTrue) {
    result = true
  } else {
    // All permissions are null
    result = false
  }

  if (!hasApplicableRole) {
    logResult({
      user,
      resource,
      action,
      result,
      data,
      details: "No appropriate role",
      forceLog,
    })
  } else if (hasFalse) {
    logResult({
      user,
      resource,
      action,
      result,
      data,
      details: "Has false",
      forceLog,
    })
  } else if (hasTrue) {
    logResult({
      user,
      resource,
      action,
      result,
      data,
      details: "Has true",
      forceLog,
    })
  } else {
    logResult({
      user,
      resource,
      action,
      result,
      data,
      details: "All null",
      forceLog,
    })
  }

  return result
}
