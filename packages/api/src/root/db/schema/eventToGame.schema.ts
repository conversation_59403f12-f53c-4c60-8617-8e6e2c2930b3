import { int, mysqlTable, text, timestamp } from "drizzle-orm/mysql-core"

import { commonId } from "./common"
import { eventSchema } from "./event.schema"
import { gamesSchema } from "./games.schema"

export const eventToGameSchema = mysqlTable("agr_game2event", {
  ...commonId,
  gameId: int("game_id")
    .notNull()
    .references(() => gamesSchema.id),
  eventId: int("event_id")
    .notNull()
    .references(() => eventSchema.id),
  lastUpdated: timestamp("last_updated"),
  added: timestamp("added"),
  users: text("agr_user_data"),
  tags: text("agr_tag_data"),
  userCount: int("user_count"),
  expansions: text("agr_expansion_data"),
  news: timestamp(),
})
