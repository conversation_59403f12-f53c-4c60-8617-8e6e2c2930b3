import { int, mysqlEnum, mysqlTable } from "drizzle-orm/mysql-core"

import { usersSchema } from "./users.schema"

export const permissionUserToRoleSchema = mysqlTable("permission_user2role", {
  userId: int("user_id")
    .notNull()
    .references(() => usersSchema.id),
  subjectId: int("subject_id"),
  roleId: mysqlEnum("role_id", [
    "user",
    "trusted",
    "unverified",
    "admin",
    "superadmin",
    "banned",
    "member",
    "moder",
    "host",
    "cohost",
    "participant",
    "interested",
    "requested",
    "unwelcome",
    "reserved",
    "owner",
    "trustedmember",
    "invited",
    "disgraced",
    "unregistered",
  ]).notNull(),
  id: int().notNull().autoincrement(),
  subject: mysqlEnum(["global", "community", "userdata", "event"]).notNull(),
})
