import * as fs from "node:fs"

import { PutObjectCommand, S3Client } from "@aws-sdk/client-s3"
import dayjs from "dayjs"
import sharp from "sharp"

const largeThumbnailSize = 100
const smallThumbnailSize = 24
const quality = {
  jpeg: { quality: 70 },
  webp: { quality: 70 },
  png: { compressionLevel: 7 },
}

export const uploadProfilePicture = async (
  file: Buffer,
  userId: number,
  fileExtension: string,
): Promise<string> => {
  const client = new S3Client({
    region: process.env.AWS_REGION,
    credentials: {
      accessKeyId: process.env.AWS_ACCESS_KEY_ID ?? "",
      secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY ?? "",
    },
  })

  try {
    const sharpImage = sharp(file)
    const metadata = await sharpImage.metadata()

    const { format } = metadata

    let toResize = sharpImage

    if (format && format in quality) {
      switch (format) {
        case "png":
          toResize = sharpImage.png(quality.png)
          break
        case "webp":
          toResize = sharpImage.webp(quality.webp)
          break
        case "jpeg":
          toResize = sharpImage.jpeg(quality.jpeg)
          break
        default:
          toResize = sharpImage
      }
    }

    const temp = "./temp"

    if (!fs.existsSync(temp)) {
      fs.mkdirSync(temp)
    }

    const tempPath = `./temp/${dayjs().format("YYYY-MM-DD")}`
    if (!fs.existsSync(tempPath)) {
      fs.mkdirSync(tempPath)
    }

    const largeFileName = `${tempPath}/large_${userId}.${fileExtension}`

    await toResize
      .resize({
        width: largeThumbnailSize,
        height: largeThumbnailSize,
        fit: "cover",
        position: "center",
      })
      .toFile(largeFileName)

    const smallFileName = `${tempPath}/small_${userId}.${fileExtension}`
    await toResize
      .resize({
        width: smallThumbnailSize,
        height: smallThumbnailSize,
        fit: "cover",
        position: "center",
      })
      .toFile(smallFileName)

    const command = new PutObjectCommand({
      Bucket: process.env.S3_BUCKET ?? "",
      Key: `profilePictures/${userId}.${fileExtension}`,
      Body: file,
      ACL: "public-read",
    })

    await client.send(command)

    const commandLarge = new PutObjectCommand({
      Bucket: process.env.S3_BUCKET ?? "",
      Key: `profilePictures/large_${userId}.${fileExtension}`,
      Body: fs.readFileSync(largeFileName),
      ACL: "public-read",
    })

    await client.send(commandLarge)

    const commandSmall = new PutObjectCommand({
      Bucket: process.env.S3_BUCKET ?? "",
      Key: `profilePictures/small_${userId}.${fileExtension}`,
      Body: fs.readFileSync(smallFileName),
      ACL: "public-read",
    })

    await client.send(commandSmall)
  } catch (error) {
    console.error(error)
    throw error
  }

  return "OK"
}
