import { communityBasic } from "./queries/community/communityBasic"
import { communityExpanded2 } from "./queries/community/communityExpanded2"
import { configureCommunity } from "./queries/community/do/configureCommunity"
import { createInvite } from "./queries/community/do/createInvite"
import { communityInvites } from "./queries/community/events/communityInvites"
import { confirmCommunityEventInvite } from "./queries/community/events/do/confirmCommunityEventInvite"
import { createEvent } from "./queries/community/events/do/createEvent"
import { declineCommunityEventInvite } from "./queries/community/events/do/declineCommunityEventInvite"
import { communityGameExtended2 } from "./queries/community/games/communityGameExtended2"
import { communityGamesList2 } from "./queries/community/games/communityGamesList2"
import { communityUserInfo } from "./queries/community/user/communityUserInfo"
import { communityUserList } from "./queries/community/user/communityUserList"
import { changeUserStatus } from "./queries/community/user/do/changeUserStatus"
import { switchGameShare } from "./queries/community/user/do/switchGameShare"
import { cancelInviteCommunity } from "./queries/event/community/do/cancelInviteCommunity"
import { inviteCommunity } from "./queries/event/community/do/inviteCommunity"
import { configureEvent } from "./queries/event/do/configureEvent"
import { eventExtended } from "./queries/event/eventExtended"
import { eventOrganizers } from "./queries/event/eventOrganizers"
import { updateGameChangeStatus } from "./queries/event/games/do/updateGameChangeStatus"
import { eventGamesInfo } from "./queries/event/games/eventGamesInfo"
import { eventGamesList } from "./queries/event/games/eventGamesList"
import { changeUserEventStatus } from "./queries/event/participant/do/changeUserEventStatus"
import { eventParticipant } from "./queries/event/participant/eventParticipant"
import { eventParticipantList } from "./queries/event/participant/eventParticipantList"
import { eventWizzardCreatePreset } from "./queries/event/wizzard/do/eventWizzardCreatePreset"
import { eventWizzardUpdateSettings } from "./queries/event/wizzard/do/eventWizzardUpdateSettings"
import { eventWizzardUpdateSettingsExtended } from "./queries/event/wizzard/do/eventWizzardUpdateSettingsExtended"
import { eventWizzardSettings } from "./queries/event/wizzard/eventWizzardSettings"
import { loggedInUser } from "./queries/login/do/loggedInUser"
import { communityList } from "./queries/protected/community/communityList"
import { createCommunity } from "./queries/protected/community/do/createCommunity"
import { joinByInvite } from "./queries/protected/community/do/joinByInvite"
import { joinByInvitePrejoin } from "./queries/protected/community/do/joinByInvitePrejoin"
import { joinCommunity } from "./queries/protected/community/do/joinCommunity"
import { publicCommunityList } from "./queries/protected/community/publicCommunityList"
import { createPrivateEvent } from "./queries/protected/event/do/createPrivateEvent"
import { protectedEventDeletePreset } from "./queries/protected/event/do/protectedEventDeletePreset"
import { eventBasic } from "./queries/protected/event/eventBasic"
import { eventsList } from "./queries/protected/event/eventsList"
import { getItemInfo } from "./queries/protected/game/getItemInfo"
import { searchCommunities } from "./queries/protected/search/searchCommunities"
import { searchUser } from "./queries/protected/search/searchUser"
import { logoutUser } from "./queries/protected/user/do/logoutUser"
import { getMyGameInfo } from "./queries/protected/user/getMyGameInfo"
import { getMyInfo } from "./queries/protected/user/getMyInfo"
import { publicEventBasic } from "./queries/public/event/eventBasic"
import { publicEventExtended } from "./queries/public/event/eventExtended"
import { tagList } from "./queries/public/tagList"
import { changeMyEventStatus } from "./queries/user/do/changeMyEventStatus"
import { leaveCommunity } from "./queries/user/do/leaveCommunity"
import { userProfileUpdate } from "./queries/user/do/userProfileUpdate"
import { router } from "./trpc/trpc"

export const appRouter = router({
  login: {
    do: {
      login: loggedInUser,
    },
  },
  event: {
    wizzard: {
      settings: eventWizzardSettings,

      do: {
        createPreset: eventWizzardCreatePreset,
        updateSettings: eventWizzardUpdateSettings,
        updateSettingsExtended: eventWizzardUpdateSettingsExtended,
      },
    },
    extended: eventExtended,
    organizers: eventOrganizers,
    participant: {
      info: eventParticipant,
      list: eventParticipantList,
      do: {
        updateStatus: changeUserEventStatus,
      },
    },
    community: {
      do: {
        invite: inviteCommunity,
        cancelInvite: cancelInviteCommunity,
      },
    },
    games: {
      list: eventGamesList,
      info: eventGamesInfo,
      do: {
        updateStatus: updateGameChangeStatus,
      },
      /*
      do: {
        addGameWish: eventGamesAddGameWish, // add games from BGG (search for ANY game to add as wishlist). If only BGG - consider adding to DB(???) Limit to ~5 games per user? ~100 games per event?
        removeGameWish: eventGamesRemoveGameWish, // remove games from BGG
        addGameComment: eventGamesAddGameComment,
      },
       */
    },
    do: {
      update: configureEvent,
    },
  },
  community: {
    basic: communityBasic,
    expanded: communityExpanded2,
    games: {
      list: communityGamesList2,
      game: communityGameExtended2,
    },
    user: {
      list: communityUserList,
      info: communityUserInfo,
      do: {
        updateStatus: changeUserStatus,
        updateGameShare: switchGameShare,
      },
    },
    events: {
      invites: communityInvites,
      do: {
        create: createEvent,
        confirmInvite: confirmCommunityEventInvite,
        declineInvite: declineCommunityEventInvite,
      },
    },
    do: {
      update: configureCommunity,
      invite: createInvite,
    },
  },
  protected: {
    search: {
      communities: searchCommunities,
      users: searchUser,
    },
    do: {
      createCommunity,
    },
    community: {
      list: communityList,
      publicList: publicCommunityList,
      do: {
        create: createCommunity,
        join: joinCommunity,
        inviteJoin: joinByInvite,
        inviteJoinPrejoin: joinByInvitePrejoin,
      },
    },
    game: {
      itemInfo: getItemInfo,
    },
    event: {
      list: eventsList,
      basic: eventBasic,
      do: {
        deletePreset: protectedEventDeletePreset,
        create: createPrivateEvent,
      },
    },
    user: {
      info: getMyInfo,
      games: getMyGameInfo,
      do: {
        logout: logoutUser,
        updateProfile: userProfileUpdate,
        updateEventStatus: changeMyEventStatus,
        leaveCommunity,
      },
    },
  },
  public: {
    tags: {
      list: tagList,
    },
    event: {
      basic: publicEventBasic,
      extended: publicEventExtended,
    },
  },
}) // ...

export type AppRouter = typeof appRouter
