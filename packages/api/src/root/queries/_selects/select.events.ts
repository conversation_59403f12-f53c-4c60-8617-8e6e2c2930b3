import { eventSchema } from "../../db/schema/event.schema"
import { usersSchema } from "../../db/schema/users.schema"

export const selectEvents = {
  id: eventSchema.id,
  title: eventSchema.title,
  starts: eventSchema.starts,
  ends: eventSchema.ends,
  state: eventSchema.state,
  openness: eventSchema.openness,
  maxCapacity: eventSchema.maxCapacity,
  reserveCapacity: eventSchema.reserveCapacity,
  going: eventSchema.going,
  reserve: eventSchema.reserve,
  smallDescription: eventSchema.smallDescription,
  minCapacity: eventSchema.minCapacity,
  hasAgeLimit: eventSchema.hasAgeLimit,
  hostId: usersSchema.id,
  hostName: usersSchema.name,
  hostAvatar: usersSchema.avatar,
  hostColor: usersSchema.color,
  image: eventSchema.image,
  memberApproval: eventSchema.memberApproval,
  lat: eventSchema.lat,
  lng: eventSchema.lng,
  location: eventSchema.location,
}
