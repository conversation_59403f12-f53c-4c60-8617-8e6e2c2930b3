import { eq } from "drizzle-orm"
import { z } from "zod"

import { db } from "../../../db"
import { usersSchema } from "../../../db/schema/users.schema"
import { protectedProcedure } from "../../../trpc/procedures/protectedProcedure"
import { OK_RESPONSE } from "../../_helpers/responses"

export const userProfileUpdate = protectedProcedure
  .input(
    z.object({
      name: z.string().min(3),
      bggUsername: z.union([
        z.literal(""),
        z.string().min(4).max(20).optional(),
      ]),
      email: z.union([z.literal(""), z.string().email()]),
      color: z.string().regex(/^[0-9a-f]{6}$/i),
      active: z.boolean(),
    }),
  )
  .mutation(async ({ input, ctx: { loginData } }) => {
    db.update(usersSchema)
      .set({
        bggUsername: input.bggUsername,
        name: input.name,
        email: input.email === "" ? null : input.email,
        active: input.active,
        color: input.color,
      })
      .where(eq(usersSchema.id, loginData.id))
      .then()

    return OK_RESPONSE
  })
