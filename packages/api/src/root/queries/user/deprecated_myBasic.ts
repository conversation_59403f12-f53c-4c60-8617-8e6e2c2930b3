import { eq } from "drizzle-orm"

import { db } from "../../db"
import { permissionUserToRoleSchema } from "../../db/schema/permissionUserToRole.schema"
import { usersSchema } from "../../db/schema/users.schema"
import { protectedProcedure } from "../../trpc/procedures/protectedProcedure"

export const getMyInfo = protectedProcedure.query(
  async ({ ctx: { loginData } }) => {
    const profile = await db
      .select({
        id: usersSchema.id,
        name: usersSchema.name,
        color: usersSchema.color,
        avatar: usersSchema.avatar,
      })
      .from(usersSchema)
      .where(eq(usersSchema.id, loginData.id))
      .then((user) => user[0])

    const roles = await db
      .select({
        role: permissionUserToRoleSchema.roleId,
        subject: permissionUserToRoleSchema.subject,
        subjectId: permissionUserToRoleSchema.subjectId,
      })
      .from(permissionUserToRoleSchema)
      .where(eq(permissionUserToRoleSchema.userId, loginData.id))
      .then((roles) => roles)

    return { ...profile, roles }
  },
)
