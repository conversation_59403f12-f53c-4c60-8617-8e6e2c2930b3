import { eq } from "drizzle-orm"

import { db } from "../../db"
import { settingsSchema } from "../../db/schema/settingsSchema"

export const communityImagePath = async () =>
  await db
    .select({
      name: settingsSchema.name,
      value: settingsSchema.value,
    })
    .from(settingsSchema)
    .where(eq(settingsSchema.name, "img:community_image_path"))
    .then((setting) => setting[0]?.value)
