import { and, eq } from "drizzle-orm"

import { Role } from "../../../../../common/src/permissions/roles/helpers/types"
import { db } from "../../db"
import { permissionUserToRoleSchema } from "../../db/schema/permissionUserToRole.schema"

export const removeRoles = async (
  userId: number,
  subject: "global" | "community" | "userdata" | "event",
  subjectId: number,
  roleId?: Role,
) => {
  await db
    .delete(permissionUserToRoleSchema)
    .where(
      and(
        eq(permissionUserToRoleSchema.userId, userId),
        eq(permissionUserToRoleSchema.subject, subject),
        eq(permissionUserToRoleSchema.subjectId, subjectId),
        roleId ? eq(permissionUserToRoleSchema.roleId, roleId) : undefined,
      ),
    )
}
