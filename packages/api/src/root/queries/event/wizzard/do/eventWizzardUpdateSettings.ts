import { TRPCError } from "@trpc/server"
import { and, eq } from "drizzle-orm"
import { z } from "zod"

import { hasPermission } from "../../../../../../../common/src/permissions/hasPermissions"
import { db } from "../../../../db"
import { userToEventSchema } from "../../../../db/schema/userToEvent.schema"
import { eventProcedure } from "../../../../trpc/procedures/eventProcedure"
import { getUser } from "../../../_subqueries/user/getUser"

export const eventWizzardUpdateSettings = eventProcedure
  .input(
    z.object({
      eventId: z.number(),
      share: z.boolean(),
      canRequest: z.boolean(),
      userId: z.number().optional(),
    }),
  )
  .mutation(async ({ input, ctx: { loginData, event } }) => {
    const user = input.userId ? await getUser(input.userId) : loginData

    if (!user) {
      throw new TRPCError({
        code: "NOT_FOUND",
        message: "No user found",
      })
    }

    let canAllowShare = hasPermission(user, "event", "shareUserGames", event)

    if (user.id !== loginData?.id) {
      const permission = hasPermission(loginData, "event", "allowShare", event)

      canAllowShare = permission

      if (permission) {
        if (event.share === "hosts") {
          canAllowShare = hasPermission(user, "event", "isSuper", event)
        }
      }
    }

    if (!canAllowShare) {
      throw new TRPCError({
        code: "FORBIDDEN",
        message: "You can't change this setting",
      })
    }

    await db
      .update(userToEventSchema)
      .set({
        shareGames: input.share,
        canRequest: input.canRequest,
      })
      .where(
        and(
          eq(userToEventSchema.userId, loginData.id),
          eq(userToEventSchema.eventId, event.id),
        ),
      )
      .then((re) => re)
  })
