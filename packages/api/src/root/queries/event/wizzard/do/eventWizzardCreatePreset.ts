import { TRPCError } from "@trpc/server"
import { and, eq } from "drizzle-orm"
import { z } from "zod"

import { db } from "../../../../db"
import { eventListPreset } from "../../../../db/schema/eventListPreset"
import { userToEventSchema } from "../../../../db/schema/userToEvent.schema"
import { userToGameToEventSchema } from "../../../../db/schema/userToGameToEvent.schema"
import { eventProcedure } from "../../../../trpc/procedures/eventProcedure"

export const eventWizzardCreatePreset = eventProcedure
  .input(
    z.object({
      eventId: z.number(),
      title: z.string(),
    }),
  )
  .mutation(async ({ input, ctx: { loginData, event } }) => {
    const eventSettings = await db
      .select({
        share: userToEventSchema.shareGames,
        canRequest: userToEventSchema.canRequest,
      })
      .from(userToEventSchema)
      .where(
        and(
          eq(userToEventSchema.userId, loginData.id),
          eq(userToEventSchema.eventId, event.id),
        ),
      )
      .then((event) => event[0])

    if (!eventSettings || !eventSettings.share) {
      throw new TRPCError({
        code: "FORBIDDEN",
        message: "You can't create preset",
      })
    }

    const userEventGames = await db
      .select({
        id: userToGameToEventSchema.gameId,
        status: userToGameToEventSchema.status,
      })
      .from(userToGameToEventSchema)
      .where(
        and(
          eq(userToGameToEventSchema.eventId, event.id),
          eq(userToGameToEventSchema.userId, loginData.id),
        ),
      )
      .then((games) => games)

    await db.insert(eventListPreset).values({
      userId: loginData.id,
      title: input.title,
      games: JSON.stringify({
        games: userEventGames.filter(
          (game) =>
            game.status === "willbring" ||
            game.status === "canask" ||
            game.status === "willnotbring",
        ),
        settings: {
          canRequest: eventSettings.canRequest,
        },
      }),
    })
  })
