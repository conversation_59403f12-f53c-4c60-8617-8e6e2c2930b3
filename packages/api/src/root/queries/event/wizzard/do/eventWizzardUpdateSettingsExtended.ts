import { TRPCError } from "@trpc/server"
import { and, eq } from "drizzle-orm"
import { z } from "zod"

import { db } from "../../../../db"
import { eventListPreset } from "../../../../db/schema/eventListPreset"
import { userToBaseGameSchema } from "../../../../db/schema/userToBaseGame.schema"
import { userToEventSchema } from "../../../../db/schema/userToEvent.schema"
import { eventProcedure } from "../../../../trpc/procedures/eventProcedure"
import { EventPresetGame } from "../../../../types/game.types"
import { setGameState } from "../../../_subqueries/event/setGameState"

type EventStateValue =
  (typeof userToEventSchema.wizzardState)["enumValues"][number]

export const eventWizzardUpdateSettingsExtended = eventProcedure
  .input(
    z.object({
      isDone: z.boolean().optional(),
      eventId: z.number(),
      presetId: z.number().nullable().optional(),
      prevStep: z.boolean().optional(),
    }),
  )
  .mutation(async ({ input, ctx: { loginData, event } }) => {
    const eventState = await db
      .select({
        wizzardState: userToEventSchema.wizzardState,
        canRequest: userToEventSchema.canRequest,
        share: userToEventSchema.shareGames,
      })
      .from(userToEventSchema)
      .where(
        and(
          eq(userToEventSchema.userId, loginData.id),
          eq(userToEventSchema.eventId, event.id),
        ),
      )
      .then((event) => event[0])

    if (!eventState || eventState.wizzardState === "skip") {
      throw new TRPCError({
        code: "NOT_FOUND",
        message: "No settings found",
      })
    }

    if (
      input.presetId &&
      (eventState.wizzardState === "waiting" ||
        eventState.wizzardState === "settings")
    ) {
      const preset = await db
        .select({
          games: eventListPreset.games,
        })
        .from(eventListPreset)
        .where(
          and(
            eq(eventListPreset.userId, loginData.id),
            eq(eventListPreset.id, input.presetId),
          ),
        )
        .then((preset) => preset[0])

      if (!preset) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Preset not found",
        })
      }

      const { games, settings }: EventPresetGame = JSON.parse(
        preset.games ?? "[]",
      )

      await Promise.all(
        games.map(async (game) => {
          await setGameState({
            userId: loginData.id,
            eventId: event.id,
            gameId: game.id,
            status: game.status,
          })
        }),
      )

      if (settings.canRequest !== eventState.canRequest) {
        const allGames = await db
          .select({
            id: userToBaseGameSchema.gameId,
          })
          .from(userToBaseGameSchema)
          .where(and(eq(userToBaseGameSchema.userId, loginData.id)))
          .then((games) => games)

        await Promise.all(
          allGames.map(async (game) => {
            if (games.find((g) => g.id === game.id)) {
              return
            }

            await setGameState({
              userId: loginData.id,
              eventId: event.id,
              gameId: game.id,
              status: settings.canRequest ? "canask" : "willnotbring",
            })
          }),
        )
      }
    }

    const steps: Record<EventStateValue, EventStateValue> = {
      skip: "skip",
      waiting: "settings",
      settings: !eventState.share ? "done" : "pick-games",
      "pick-games": "review-games",
      "review-games": "done",
      done: "done",
    }

    const currentState = eventState.wizzardState ?? "settings"

    await db
      .update(userToEventSchema)
      .set({
        wizzardState: input.prevStep
          ? ((Object.keys(steps) as EventStateValue[]).find(
              (key) => steps[key] === currentState,
            ) ?? "settings")
          : steps[currentState],
      })
      .where(
        and(
          eq(userToEventSchema.userId, loginData.id),
          eq(userToEventSchema.eventId, event.id),
        ),
      )
      .then((re) => re)
  })
