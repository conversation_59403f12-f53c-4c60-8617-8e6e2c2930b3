import { TRPCError } from "@trpc/server"
import { and, eq, or } from "drizzle-orm"
import { z } from "zod"

import { hasPermission } from "../../../../../common/src/permissions/hasPermissions"
import { db } from "../../db"
import { communitySchema } from "../../db/schema/community.schema"
import { communityToEventSchema } from "../../db/schema/communityToEvent.schema"
import { permissionUserToRoleSchema } from "../../db/schema/permissionUserToRole.schema"
import { usersSchema } from "../../db/schema/users.schema"
import { eventProcedure } from "../../trpc/procedures/eventProcedure"
import { getCommunityDetails } from "../_subqueries/community/getCommunityDetails"
import { getUserRoles } from "../_subqueries/user/getUserRoles"

export const eventOrganizers = eventProcedure
  .input(z.object({ eventId: z.number() }))
  .query(async ({ input, ctx: { loginData, event } }) => {
    if (!hasPermission(loginData, "event", "view", event)) {
      throw new TRPCError({
        code: "FORBIDDEN",
        message: "You can't view this event",
      })
    }

    const communities = await db
      .selectDistinct({
        id: communitySchema.id,
        name: communitySchema.name,
        image: communitySchema.image,
        owner: communityToEventSchema.owner,
        location: communitySchema.location,
        online: communitySchema.online,
        openness: communitySchema.openness,
        approval: communitySchema.memberApproval,
        share: communitySchema.allowShare,
        invite: communityToEventSchema.invite,
      })
      .from(communityToEventSchema)
      .innerJoin(
        communitySchema,
        eq(communityToEventSchema.communityId, communitySchema.id),
      )
      .innerJoin(
        permissionUserToRoleSchema,
        and(
          eq(permissionUserToRoleSchema.subjectId, event.id),
          eq(permissionUserToRoleSchema.subject, "event"),
          eq(permissionUserToRoleSchema.userId, loginData.id),
        ),
      )
      .where(
        and(
          eq(communityToEventSchema.eventId, input.eventId),
          or(
            eq(permissionUserToRoleSchema.roleId, "cohost"),
            eq(permissionUserToRoleSchema.roleId, "host"),
            eq(communityToEventSchema.invite, false),
          ),
        ),
      )
      .then((communities) =>
        Promise.all(
          communities.map(async (community) => ({
            ...community,
            ...(await getCommunityDetails({ community, loginData })),
          })),
        ),
      )

    const hosts = await db
      .select({
        id: usersSchema.id,
        name: usersSchema.name,
        avatar: usersSchema.avatar,
        color: usersSchema.color,
      })
      .from(permissionUserToRoleSchema)
      .innerJoin(
        usersSchema,
        eq(permissionUserToRoleSchema.userId, usersSchema.id),
      )
      .where(
        and(
          eq(permissionUserToRoleSchema.subjectId, input.eventId),
          eq(permissionUserToRoleSchema.subject, "event"),
          or(
            eq(permissionUserToRoleSchema.roleId, "host"),
            eq(permissionUserToRoleSchema.roleId, "cohost"),
          ),
        ),
      )
      .then((data) =>
        Promise.all(
          data.map(async (user) => ({
            ...user,
            roles: await getUserRoles(user.id),
          })),
        ),
      )

    return { communities, hosts }
  })
