import { TRPCError } from "@trpc/server"
import { and, eq, inArray, or } from "drizzle-orm"
import { z } from "zod"

import { db } from "../../../db"
import { eventToGameSchema } from "../../../db/schema/eventToGame.schema"
import { gameToExpansionSchema } from "../../../db/schema/gameToExpansion.schema"
import { gameToTagSchema } from "../../../db/schema/gameToTag.schema"
import { gamesSchema } from "../../../db/schema/games.schema"
import { itemToEventSchema } from "../../../db/schema/itemToEvent.schema"
import { tagCategoriesSchema } from "../../../db/schema/tagCategories.schema"
import { tagsSchema } from "../../../db/schema/tags.schema"
import { userToBaseGameSchema } from "../../../db/schema/userToBaseGame.schema"
import { userToEventSchema } from "../../../db/schema/userToEvent.schema"
import { userToGameSchema } from "../../../db/schema/userToGame.schema"
import { userToGameToEventSchema } from "../../../db/schema/userToGameToEvent.schema"
import { usersSchema } from "../../../db/schema/users.schema"
import { eventProcedure } from "../../../trpc/procedures/eventProcedure"
import { BggGameDataExtracted } from "../../../types/game.types"
import { extractGameData, stripExtracted } from "../../_helpers/extractGameData"

export const eventGamesInfo = eventProcedure
  .input(z.object({ gameId: z.number(), eventId: z.number() }))
  .query(async ({ input, ctx: { event, loginData } }) => {
    let game = await db
      .select({
        id: gamesSchema.id,
        title: gamesSchema.title,
        bggId: gamesSchema.bggId,
        bggInfo: gamesSchema.bggInfo,
        users: eventToGameSchema.users,
        eventStatus: userToGameToEventSchema.status,
        canRequest: userToEventSchema.canRequest,
        share: userToEventSchema.shareGames,
        userOwns: userToBaseGameSchema.gameId,
        userEvent: userToEventSchema.eventId,
      })
      .from(gamesSchema)
      .innerJoin(
        eventToGameSchema,
        eq(eventToGameSchema.gameId, gamesSchema.id),
      )
      .leftJoin(
        userToBaseGameSchema,
        and(
          eq(userToBaseGameSchema.gameId, gamesSchema.id),
          eq(userToBaseGameSchema.userId, loginData.id),
        ),
      )
      .leftJoin(
        userToEventSchema,
        and(
          eq(userToEventSchema.userId, loginData.id),
          eq(userToEventSchema.eventId, event.id),
        ),
      )
      .leftJoin(
        userToGameToEventSchema,
        and(
          eq(userToGameToEventSchema.userId, loginData.id),
          eq(userToGameToEventSchema.gameId, gamesSchema.id),
          eq(userToGameToEventSchema.eventId, event.id),
        ),
      )
      .where(
        and(
          eq(gamesSchema.id, input.gameId),
          eq(eventToGameSchema.eventId, event.id),
        ),
      )
      .then(async (loadGame) => {
        if (!loadGame[0]) {
          return null
        }

        const game = loadGame[0]

        const { bggDataExtracted, users: userIds } = extractGameData(game)

        const users = await db
          .select({
            id: usersSchema.id,
            name: usersSchema.name,
            willTeach: userToBaseGameSchema.willTeach,
            experience: userToBaseGameSchema.experience,
            lastPlay: userToBaseGameSchema.lastPlay,
            playCount: userToBaseGameSchema.playCount,
            rating: userToBaseGameSchema.rating,
            color: usersSchema.color,
            avatar: usersSchema.avatar,
            portability: userToBaseGameSchema.portability,
            events: userToBaseGameSchema.events,
            playPriority: userToBaseGameSchema.playPriority,
            eventStatus: userToGameToEventSchema.status,
          })
          .from(usersSchema)
          .leftJoin(
            userToBaseGameSchema,
            and(
              eq(userToBaseGameSchema.userId, usersSchema.id),
              eq(userToBaseGameSchema.gameId, input.gameId),
              eq(userToBaseGameSchema.deleted, false),
            ),
          )
          .innerJoin(
            userToGameToEventSchema,
            and(
              eq(userToGameToEventSchema.userId, usersSchema.id),
              eq(userToGameToEventSchema.gameId, input.gameId),
              eq(userToGameToEventSchema.eventId, input.eventId),
            ),
          )
          .where(
            or(
              inArray(
                usersSchema.id,
                userIds.map((user) => user[0]),
              ),
              eq(usersSchema.id, loginData.id),
            ),
          )
          .then((users) => users)

        return {
          ...game,
          ...stripExtracted,
          players: {
            box: {
              min: parseInt(bggDataExtracted.players.box.min ?? "0"),
              max: parseInt(bggDataExtracted.players.box.max ?? "0"),
            },
            stats: bggDataExtracted.players.stats?.map((stat) => [
              parseInt(stat.players),
              stat.status,
              parseInt(stat.players).toString() != stat.players,
            ]),
          },
          length: {
            box: {
              min: parseInt(bggDataExtracted.playTime.box.min ?? "0"),
              max: parseInt(bggDataExtracted.playTime.box.max ?? "0"),
            },
          },
          age: bggDataExtracted.age,
          average: Math.round(parseFloat(bggDataExtracted.average) * 100) / 100,
          users,
        }
      })

    game = game
      ? game
      : await db
          .select({
            id: gamesSchema.id,
            title: gamesSchema.title,
            bggId: gamesSchema.bggId,
            bggInfo: gamesSchema.bggInfo,
          })
          .from(gamesSchema)
          .leftJoin(
            userToBaseGameSchema,
            and(
              eq(userToBaseGameSchema.userId, loginData.id),
              eq(userToBaseGameSchema.gameId, gamesSchema.id),
            ),
          )
          .where(eq(gamesSchema.id, input.gameId))
          .then(async (loadGame) => {
            if (!loadGame[0]) {
              return null
            }

            const game = loadGame[0]
            const bggDataExtracted: BggGameDataExtracted = JSON.parse(
              game.bggInfo ?? "{}",
            ) as BggGameDataExtracted

            return {
              id: game.id,
              title: game.title,
              bggId: game.bggId,
              eventStatus: null,
              canRequest: true,
              share: true,
              userOwns: game.id,
              userEvent: event.id,
              users: [],
              tags: undefined,
              expansions: undefined,
              bggInfo: undefined,
              players: {
                box: {
                  min: parseInt(bggDataExtracted.players.box.min ?? "0"),
                  max: parseInt(bggDataExtracted.players.box.max ?? "0"),
                },
                stats: bggDataExtracted.players.stats?.map((stat) => [
                  parseInt(stat.players),
                  stat.status,
                  parseInt(stat.players).toString() != stat.players,
                ]),
              },
              length: {
                box: {
                  min: parseInt(bggDataExtracted.playTime.box.min ?? "0"),
                  max: parseInt(bggDataExtracted.playTime.box.max ?? "0"),
                },
              },
              age: bggDataExtracted.age,
              average:
                Math.round(parseFloat(bggDataExtracted.average) * 100) / 100,
            }
          })

    if (!game) {
      throw new TRPCError({
        code: "NOT_FOUND",
        message: "Game not found",
      })
    }

    const expansions = await db
      .select({
        id: gamesSchema.id,
        title: gamesSchema.title,
        bggId: gamesSchema.bggId,
        bggInfo: gamesSchema.bggInfo,
        users: itemToEventSchema.users,
        tags: itemToEventSchema.tags,
        news: itemToEventSchema.news,
        userGameId: userToGameSchema.id,
        type: gamesSchema.type,
      })
      .from(gameToExpansionSchema)
      .innerJoin(
        gamesSchema,
        eq(gameToExpansionSchema.expansionId, gamesSchema.id),
      )
      .leftJoin(
        itemToEventSchema,
        and(
          eq(gameToExpansionSchema.expansionId, itemToEventSchema.gameId),
          eq(itemToEventSchema.eventId, input.eventId),
        ),
      )
      .leftJoin(
        userToGameSchema,
        and(
          eq(userToGameSchema.gameId, gamesSchema.id),
          eq(userToGameSchema.userId, loginData.id),
          eq(userToGameSchema.deleted, false),
        ),
      )
      .where(eq(gameToExpansionSchema.gameId, input.gameId))
      .then((games) => {
        return games
          .filter((game) => game.userGameId !== null || game.users !== null) // check if its in current users collection or event library
          .map((game) => {
            const users: number[] = JSON.parse(game.users ?? "[]") as number[]

            const tags: number[] = JSON.parse(game.tags ?? "[]") as number[]

            const bggDataExtracted: BggGameDataExtracted = JSON.parse(
              game.bggInfo ?? "{}",
            ) as BggGameDataExtracted

            return {
              type: game.type,
              id: game.id,
              title: game.title,
              bggId: game.bggId,
              news: game.news,
              userOwn: game.userGameId !== null,
              average: bggDataExtracted.average
                ? Math.round(parseFloat(bggDataExtracted.average) * 100) / 100
                : null,
              users,
              tags,
            }
          })
      })

    const tags = await db
      .select({
        id: tagsSchema.id,
        type: tagCategoriesSchema.title,
        typeId: tagsSchema.type,
        title: tagsSchema.title,
        color: tagCategoriesSchema.color,
      })
      .from(gameToTagSchema)
      .innerJoin(tagsSchema, eq(gameToTagSchema.tagId, tagsSchema.id))
      .innerJoin(
        tagCategoriesSchema,
        eq(tagsSchema.type, tagCategoriesSchema.id),
      )
      .where(eq(gameToTagSchema.gameId, input.gameId))
      .then((tags) => {
        return tags
      })
    return {
      tags,
      game,
      expansions,
    }
  })
