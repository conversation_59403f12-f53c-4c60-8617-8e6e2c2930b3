import { z } from "zod"

import { eventProcedure } from "../../../../trpc/procedures/eventProcedure"
import { OK_RESPONSE } from "../../../_helpers/responses"
import { setGameState } from "../../../_subqueries/event/setGameState"

export const updateGameChangeStatus = eventProcedure
  .input(
    z.object({
      eventId: z.number(),
      gameId: z.number(),
      status: z.enum([
        "wish",
        "willbring",
        "canask",
        "willnotbring",
        "willplay",
        "reset",
        "maybeplay",
      ]),
    }),
  )
  .mutation(async ({ input, ctx: { loginData, event } }) => {
    await setGameState({
      userId: loginData.id,
      eventId: event.id,
      gameId: input.gameId,
      status: input.status,
    })

    return OK_RESPONSE
  })
