import { and, desc, eq, inArray } from "drizzle-orm"
import { z } from "zod"

import { Role } from "../../../../../../common/src/permissions/roles/helpers/types"
import { db } from "../../../db"
import { eventToGameSchema } from "../../../db/schema/eventToGame.schema"
import { gamesSchema } from "../../../db/schema/games.schema"
import { permissionUserToRoleSchema } from "../../../db/schema/permissionUserToRole.schema"
import { userToBaseGameSchema } from "../../../db/schema/userToBaseGame.schema"
import { userToEventSchema } from "../../../db/schema/userToEvent.schema"
import { userToGameToEventSchema } from "../../../db/schema/userToGameToEvent.schema"
import { usersSchema } from "../../../db/schema/users.schema"
import { eventProcedure } from "../../../trpc/procedures/eventProcedure"
import { extractGameData, stripExtracted } from "../../_helpers/extractGameData"
import { selectGameData } from "../../_selects/select.gameData"
import { tagsAndCats } from "../../_subqueries/game/tagsAndCats"

export const eventGamesList = eventProcedure
  .input(z.object({ eventId: z.number() }))
  .query(async ({ input, ctx: { loginData, event } }) => {
    const games = await db
      .select({
        ...selectGameData,
        tags: eventToGameSchema.tags,
        news: eventToGameSchema.added,
        users: eventToGameSchema.users,
        userOwns: userToBaseGameSchema.gameId,
        eventStatus: userToGameToEventSchema.status,
        canRequest: userToEventSchema.canRequest,
        userEvent: userToEventSchema.eventId,
        share: userToEventSchema.shareGames,
      })
      .from(eventToGameSchema)
      .innerJoin(gamesSchema, eq(eventToGameSchema.gameId, gamesSchema.id))
      .leftJoin(
        userToBaseGameSchema,
        and(
          eq(userToBaseGameSchema.gameId, gamesSchema.id),
          eq(userToBaseGameSchema.userId, loginData.id),
        ),
      )
      .leftJoin(
        userToEventSchema,
        and(
          eq(userToEventSchema.userId, loginData.id),
          eq(userToEventSchema.eventId, event.id),
        ),
      )
      .leftJoin(
        userToGameToEventSchema,
        and(
          eq(userToGameToEventSchema.userId, loginData.id),
          eq(userToGameToEventSchema.gameId, gamesSchema.id),
          eq(userToGameToEventSchema.eventId, event.id),
        ),
      )
      .where(
        and(
          eq(eventToGameSchema.eventId, input.eventId ?? 0),
          eq(gamesSchema.type, "base"),
        ),
      )
      .orderBy(desc(eventToGameSchema.added))
      .then((games) => {
        return games.map((game) => {
          const { bggDataExtracted, users, tags } = extractGameData(game)

          return {
            ...game,
            ...stripExtracted,
            players: {
              box: {
                min: parseInt(bggDataExtracted.players.box.min ?? "0"),
                max: parseInt(bggDataExtracted.players.box.max ?? "0"),
              },
              stats: bggDataExtracted.players.stats?.map((stat) => [
                parseInt(stat.players),
                stat.status,
              ]),
            },
            average:
              Math.round(parseFloat(bggDataExtracted.average) * 100) / 100,
            weight: parseInt(bggDataExtracted.averageweight),
            users,
            tags,
          }
        })
      })

    const canSee: Role[] = ["host", "cohost", "participant"]

    const users = await db
      .select({
        id: usersSchema.id,
        name: usersSchema.name,
        avatar: usersSchema.avatar,
        color: usersSchema.color,
        role: permissionUserToRoleSchema.roleId,
      })
      .from(permissionUserToRoleSchema)
      .innerJoin(
        usersSchema,
        eq(permissionUserToRoleSchema.userId, usersSchema.id),
      )
      .where(
        and(
          eq(permissionUserToRoleSchema.subject, "event"),
          inArray(permissionUserToRoleSchema.roleId, canSee),
          eq(permissionUserToRoleSchema.subjectId, input.eventId ?? 0),
        ),
      )
      .then((users) => users)

    const tags = await tagsAndCats()

    return {
      ...tags,
      users,
      games,
    }
  })
