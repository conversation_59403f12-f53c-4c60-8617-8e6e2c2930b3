import { TRPCError } from "@trpc/server"
import { and, eq, inArray } from "drizzle-orm"
import { z } from "zod"

import { db } from "../../../db"
import { permissionUserToRoleSchema } from "../../../db/schema/permissionUserToRole.schema"
import { userToEventSchema } from "../../../db/schema/userToEvent.schema"
import { usersSchema } from "../../../db/schema/users.schema"
import { Role, hasPermission } from "../../../permissions"
import { eventProcedure } from "../../../trpc/procedures/eventProcedure"
import { tagsAndCats } from "../../_subqueries/game/tagsAndCats"
import { getSharedUserGames } from "../../_subqueries/user/getSharedUserGames"
import { getUserGames } from "../../_subqueries/user/getUserGames"

export const eventParticipant = eventProcedure
  .input(z.object({ eventId: z.number(), participantId: z.number() }))
  .query(async ({ input, ctx: { loginData, event } }) => {
    if (!hasPermission(loginData, "event", "view", event)) {
      throw new TRPCError({
        code: "UNAUTHORIZED",
        message: "Unauthorized: : You cannot see this data",
      })
    }

    const canSee: Role[] = ["interested", "participant"]

    if (hasPermission(loginData, "event", "approve", event)) {
      canSee.push("unwelcome")
      canSee.push("requested")
      canSee.push("reserved")
    }

    return db
      .select({
        id: usersSchema.id,
        name: usersSchema.name,
        bggUsername: usersSchema.bggUsername,
        color: usersSchema.color,
        avatar: usersSchema.avatar,
        share: userToEventSchema.shareGames,
        wizzardState: userToEventSchema.wizzardState,
        canRequest: userToEventSchema.canRequest,
      })
      .from(usersSchema)
      .innerJoin(
        permissionUserToRoleSchema,
        eq(permissionUserToRoleSchema.userId, usersSchema.id),
      )
      .leftJoin(
        userToEventSchema,
        and(
          eq(userToEventSchema.userId, usersSchema.id),
          eq(userToEventSchema.eventId, input.eventId),
        ),
      )
      .where(
        and(
          eq(usersSchema.id, input.participantId),
          eq(permissionUserToRoleSchema.subject, "event"),
          inArray(permissionUserToRoleSchema.roleId, canSee),
          eq(permissionUserToRoleSchema.subjectId, event.id ?? 0),
        ),
      )
      .then(async (userResponse) => {
        const user = userResponse[0]
        if (!user) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "User not found",
          })
        }

        const bggUsername = user.bggUsername

        const games =
          input.participantId === loginData.id
            ? await getUserGames(input.participantId, event.id)
            : (user.share ?? false)
              ? await getSharedUserGames(
                  input.participantId,
                  event.id,
                  user.canRequest ?? false,
                )
              : []

        const tags = await tagsAndCats()

        const roles = await db
          .select({
            role: permissionUserToRoleSchema.roleId,
            subject: permissionUserToRoleSchema.subject,
            subjectId: permissionUserToRoleSchema.subjectId,
          })
          .from(permissionUserToRoleSchema)
          .where(
            and(
              eq(permissionUserToRoleSchema.userId, user.id),
              eq(permissionUserToRoleSchema.subject, "event"),
              eq(permissionUserToRoleSchema.subjectId, event.id ?? 0),
            ),
          )
          .then((roles) => roles)

        return { ...user, bggUsername, roles, games, ...tags }
      })
  })
