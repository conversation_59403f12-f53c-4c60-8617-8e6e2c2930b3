import { TRPCError } from "@trpc/server"
import { and, eq } from "drizzle-orm"
import { z } from "zod"

import { hasPermission } from "../../../../../../common/src/permissions/hasPermissions"
import { db } from "../../../db"
import { communitySchema } from "../../../db/schema/community.schema"
import { communityToEventSchema } from "../../../db/schema/communityToEvent.schema"
import { eventSchema } from "../../../db/schema/event.schema"
import { userToEventSchema } from "../../../db/schema/userToEvent.schema"
import { usersSchema } from "../../../db/schema/users.schema"
import { publicEventProcedure } from "../../../trpc/procedures/publicEventProcedure"
import { selectEventData } from "../../_selects/select.eventData"
import { selectEvents } from "../../_selects/select.events"
import { hostInfoSchema } from "../../event/eventsListHelper"

export const publicEventBasic = publicEventProcedure
  .input(z.object({ eventId: z.number() }))
  .query(async ({ input, ctx: { loginData, isAuthenticated, event } }) => {
    const viewNonPublic = hasPermission(loginData, "event", "view", event)

    const eventData = await db
      .select({
        ...selectEventData,
        ...(viewNonPublic
          ? {
              wizzardState: userToEventSchema.wizzardState,
              userEvent: userToEventSchema.eventId,
            }
          : {}),
      })
      .from(eventSchema)
      .leftJoin(
        hostInfoSchema,
        and(
          eq(hostInfoSchema.subject, "event"),
          eq(hostInfoSchema.roleId, "host"),
          eq(hostInfoSchema.subjectId, selectEvents.id),
        ),
      )
      .leftJoin(usersSchema, eq(hostInfoSchema.userId, usersSchema.id))
      .leftJoin(
        userToEventSchema,
        eq(
          userToEventSchema.userId,
          isAuthenticated && loginData ? (loginData.id ?? 0) : 0,
        ),
      )
      .where(eq(eventSchema.id, input.eventId))
      .then(async (eventResult) => {
        if (!eventResult[0]) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "No Event found",
          })
        }

        const communities = await db
          .select({
            id: communitySchema.id,
            name: communitySchema.name,
            image: communitySchema.image,
            owner: communityToEventSchema.owner,
            openness: communitySchema.openness,
          })
          .from(communityToEventSchema)
          .innerJoin(
            communitySchema,
            eq(communityToEventSchema.communityId, communitySchema.id),
          )
          .where(eq(communityToEventSchema.eventId, eventResult[0].id))
          .then((communities) => communities)

        return { ...eventResult[0], communities, hosts: communities }
      })

    if (!eventData) {
      throw new TRPCError({
        code: "NOT_FOUND",
        message: "No Event found",
      })
    }

    return {
      ...eventData,
      isAuthenticated,
    }
  })
