import { eq } from "drizzle-orm"

import { db } from "../../../db"
import { usersSchema } from "../../../db/schema/users.schema"

import { getUserRoles } from "./getUserRoles"

export const getUser = async (userId: number) => {
  return await db
    .select({
      id: usersSchema.id,
      name: usersSchema.name,
      color: usersSchema.color,
      avatar: usersSchema.avatar,
    })
    .from(usersSchema)
    .where(eq(usersSchema.id, userId))
    .then(async (user) => {
      if (!user[0]) {
        return undefined
      }

      const roles = await getUserRoles(user[0].id)
      return { ...user[0], roles }
    })
}
