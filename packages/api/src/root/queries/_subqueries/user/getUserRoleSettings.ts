import { eq } from "drizzle-orm"

import { db } from "../../../db"
import { roleSettingsSchema } from "../../../db/schema/roleSettings.schema"

export const getUserRoleSettings = async (role: string) => {
  return await db
    .select({
      name: roleSettingsSchema.name,
      value: roleSettingsSchema.value,
    })
    .from(roleSettingsSchema)
    .where(eq(roleSettingsSchema.roleId, role))
    .then((settings) => settings)
}
