import { describe, expect, it } from 'vitest'

import { getUserGames } from './getUserGames'

describe('getUserGames', () => {
  it('should have correct types when called without eventId', async () => {
    // This test verifies that TypeScript correctly infers the return type
    // when no eventId is provided
    const mockUserId = 1
    
    // This should compile without TypeScript errors
    const games = await getUserGames(mockUserId)
    
    // Verify the structure exists (this will be a runtime test when connected to DB)
    expect(Array.isArray(games)).toBe(true)
    
    // Type assertion to verify the expected structure
    if (games.length > 0) {
      const game = games[0]
      expect(typeof game.id).toBe('number')
      expect(typeof game.title).toBe('string')
      expect(typeof game.bggId).toBe('number')
      expect(Array.isArray(game.tags)).toBe(true)
      expect(game.bggInfo).toBeUndefined()
      expect(game.eventStatus).toBeNull() // Should be null when no eventId
      expect(typeof game.players).toBe('object')
      expect(typeof game.weight).toBe('number')
      expect(typeof game.average).toBe('number')
    }
  })

  it('should have correct types when called with eventId', async () => {
    // This test verifies that TypeScript correctly infers the return type
    // when eventId is provided
    const mockUserId = 1
    const mockEventId = 1
    
    // This should compile without TypeScript errors
    const games = await getUserGames(mockUserId, mockEventId)
    
    // Verify the structure exists (this will be a runtime test when connected to DB)
    expect(Array.isArray(games)).toBe(true)
    
    // Type assertion to verify the expected structure
    if (games.length > 0) {
      const game = games[0]
      expect(typeof game.id).toBe('number')
      expect(typeof game.title).toBe('string')
      expect(typeof game.bggId).toBe('number')
      expect(Array.isArray(game.tags)).toBe(true)
      expect(game.bggInfo).toBeUndefined()
      // eventStatus can be null or a valid EventGameStatus when eventId is provided
      expect(game.eventStatus === null || typeof game.eventStatus === 'string').toBe(true)
      expect(typeof game.players).toBe('object')
      expect(typeof game.weight).toBe('number')
      expect(typeof game.average).toBe('number')
    }
  })

  it('should handle type safety for eventStatus field', () => {
    // This is a compile-time test to ensure TypeScript properly handles
    // the conditional eventStatus field
    
    // Mock function calls to test type inference
    const testWithoutEvent = async () => {
      const games = await getUserGames(1)
      // TypeScript should know that eventStatus is always null here
      games.forEach(game => {
        // This should compile - eventStatus can be null
        if (game.eventStatus === null) {
          // Valid check
        }
        // This should also compile - eventStatus can be a valid status
        if (game.eventStatus === 'wish') {
          // Valid check
        }
      })
    }

    const testWithEvent = async () => {
      const games = await getUserGames(1, 1)
      // TypeScript should know that eventStatus can be null or a valid status
      games.forEach(game => {
        // This should compile - eventStatus can be null
        if (game.eventStatus === null) {
          // Valid check
        }
        // This should also compile - eventStatus can be a valid status
        if (game.eventStatus === 'willbring') {
          // Valid check
        }
      })
    }

    // These functions should compile without TypeScript errors
    expect(typeof testWithoutEvent).toBe('function')
    expect(typeof testWithEvent).toBe('function')
  })
})
