import { and, eq, inArray, or } from "drizzle-orm"

import { db } from "../../../db"
import { communitySchema } from "../../../db/schema/community.schema"
import { communityToEventSchema } from "../../../db/schema/communityToEvent.schema"

type GetEventCommunitiesParams = {
  eventId: number
  userCommunities: number[]
}

export const getEventCommunities = async ({
  eventId,
  userCommunities,
}: GetEventCommunitiesParams) => {
  return await db
    .select({
      id: communitySchema.id,
      name: communitySchema.name,
      image: communitySchema.image,
      owner: communityToEventSchema.owner,
    })
    .from(communityToEventSchema)
    .innerJoin(
      communitySchema,
      and(eq(communityToEventSchema.communityId, communitySchema.id)),
    )
    .where(
      and(
        eq(communityToEventSchema.eventId, eventId),
        or(
          eq(communitySchema.openness, "public"),
          eq(communitySchema.openness, "publicLimited"),
          inArray(communitySchema.id, userCommunities),
        ),
      ),
    )
    .then((hosts) => {
      return hosts
    })
}
