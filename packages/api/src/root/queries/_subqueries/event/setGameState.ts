import { and, eq } from "drizzle-orm"

import { db } from "../../../db"
import { eventToGameSchema } from "../../../db/schema/eventToGame.schema"
import { userToGameToEventSchema } from "../../../db/schema/userToGameToEvent.schema"
import { EventGameStatusReset } from "../../../types/game.types"

type SetGameStateProps = {
  userId: number
  eventId: number
  gameId: number
  status: EventGameStatusReset
}
export const setGameState = async ({
  userId,
  eventId,
  gameId,
  status,
}: SetGameStateProps) => {
  const existsEntry = await db
    .select({ id: eventToGameSchema.id })
    .from(eventToGameSchema)
    .where(
      and(
        eq(eventToGameSchema.eventId, eventId),
        eq(eventToGameSchema.gameId, gameId),
      ),
    )
    .then((res) => res.length > 0)

  if (!existsEntry && status !== "reset") {
    await db
      .insert(eventToGameSchema)
      .values({
        eventId,
        gameId,
      })
      .then((re) => re)
  }

  if (status === "reset") {
    await db
      .delete(userToGameToEventSchema)
      .where(
        and(
          eq(userToGameToEventSchema.userId, userId),
          eq(userToGameToEventSchema.eventId, eventId),
          eq(userToGameToEventSchema.gameId, gameId),
        ),
      )
      .then((re) => re)
    return
  }

  await db
    .insert(userToGameToEventSchema)
    .values({
      eventId,
      gameId,
      userId,
      status,
    })
    .onDuplicateKeyUpdate({ set: { status } })
    .then((re) => re)
}
