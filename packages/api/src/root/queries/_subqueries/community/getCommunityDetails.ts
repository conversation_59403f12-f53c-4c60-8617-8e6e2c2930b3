import { hasPermission } from "../../../../../../common/src/permissions/hasPermissions"
import { communitySchema } from "../../../db/schema/community.schema"

type GetCommunityDetailsParams = {
  community: Pick<
    typeof communitySchema.$inferSelect,
    "id" | "openness" | "location" | "online"
  >
  loginData: {
    id: number
  }
}

export const getCommunityDetails = async ({
  community,
  loginData,
}: GetCommunityDetailsParams) => {
  let canSeeLocation = false

  if (
    hasPermission(loginData, "community", "view", {
      id: community.id,
      openness: community.openness,
    })
  ) {
    canSeeLocation = true
  }

  return {
    location: canSeeLocation ? community.location : undefined,
    online: canSeeLocation ? community.online : undefined,
    member: hasPermission(loginData, "community", "isMember", {
      id: community?.id,
    }),
  }
}
