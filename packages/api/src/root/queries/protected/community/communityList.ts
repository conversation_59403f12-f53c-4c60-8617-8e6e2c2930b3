import { eq } from "drizzle-orm"

import { db } from "../../../db"
import { communitySchema } from "../../../db/schema/community.schema"
import { userToCommunitySchema } from "../../../db/schema/userToCommunity.schema"
import { protectedProcedure } from "../../../trpc/procedures/protectedProcedure"
import { selectCommunityData } from "../../_selects/select.commuityData"
import { getCommunityDetails } from "../../_subqueries/community/getCommunityDetails"

export const communityList = protectedProcedure.query(
  async ({ ctx: { loginData } }) => {
    // const imagePath = await communityImagePath()

    return await db
      .select(selectCommunityData)
      .from(userToCommunitySchema)
      .innerJoin(
        communitySchema,
        eq(communitySchema.id, userToCommunitySchema.communityId),
      )
      .where(eq(userToCommunitySchema.userId, loginData.id))
      .then(async (communities) => {
        return Promise.all(
          communities.map(async (community) => ({
            ...community,
            ...(await getCommunityDetails({ community, loginData })),
          })),
        )
      })
  },
)
