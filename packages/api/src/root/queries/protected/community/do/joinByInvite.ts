import { TRPCError } from "@trpc/server"
import { and, eq, gte } from "drizzle-orm"
import { z } from "zod"

import { db } from "../../../../db"
import { communitySchema } from "../../../../db/schema/community.schema"
import { communityInvitesSchema } from "../../../../db/schema/communityInvites.schema"
import { permissionUserToRoleSchema } from "../../../../db/schema/permissionUserToRole.schema"
import { userToCommunitySchema } from "../../../../db/schema/userToCommunity.schema"
import { protectedProcedure } from "../../../../trpc/procedures/protectedProcedure"
import { OK_RESPONSE } from "../../../_helpers/responses"

export const joinByInvite = protectedProcedure
  .input(
    z.object({
      code: z.string(),
      share: z.boolean(),
    }),
  )
  .mutation(async ({ input, ctx: { loginData } }) => {
    const invite = await db
      .select({
        id: communityInvitesSchema.id,
        expiration: communityInvitesSchema.expiration,
        communityId: communityInvitesSchema.communityId,
        status: communityInvitesSchema.status,
        acceptLimit: communityInvitesSchema.acceptLimit,
        count: communityInvitesSchema.count,
        accepted: communityInvitesSchema.accepted,
      })
      .from(communityInvitesSchema)
      .where(
        and(
          gte(communityInvitesSchema.expiration, new Date()),
          eq(communityInvitesSchema.inviteString, input.code ?? ""),
        ),
      )
      .then((invite) => invite[0])

    if (!invite) {
      throw new TRPCError({
        code: "PRECONDITION_FAILED",
        message: "Invite not found!",
      })
    }

    if (
      invite.status !== "sent" ||
      (invite.acceptLimit !== null && invite.acceptLimit <= (invite.count ?? 0))
    ) {
      throw new TRPCError({
        code: "PRECONDITION_FAILED",
        message: "Valid invite required to join this community",
      })
    }

    const connectionCheck = await db
      .select({ communityId: userToCommunitySchema.communityId })
      .from(userToCommunitySchema)
      .where(
        and(
          eq(userToCommunitySchema.userId, loginData.id),
          eq(userToCommunitySchema.communityId, invite.communityId),
        ),
      )
      .then((communityId) => communityId[0])

    if (connectionCheck) {
      throw new TRPCError({
        code: "PRECONDITION_FAILED",
        message: "User already has joined this community",
      })
    }

    const community = await db
      .select({
        id: communitySchema.id,
        approval: communitySchema.memberApproval,
        share: communitySchema.allowShare,
      })
      .from(communitySchema)
      .where(eq(communitySchema.id, invite.communityId))
      .then((community) => community[0])

    db.insert(userToCommunitySchema)
      .values({
        userId: loginData.id,
        communityId: invite.communityId,
        trust: 0,
        shareMyGames: community.share ? input.share : false,
      })
      .then((re) => re)

    await db
      .delete(permissionUserToRoleSchema)
      .where(
        and(
          eq(permissionUserToRoleSchema.userId, loginData.id),
          eq(permissionUserToRoleSchema.subject, "community"),
          eq(permissionUserToRoleSchema.subjectId, invite.communityId),
        ),
      )
      .then((re) => re)

    await db
      .insert(permissionUserToRoleSchema)
      .values({
        userId: loginData.id,
        roleId: "member",
        subject: "community",
        subjectId: invite.communityId,
      })
      .then((re) => re)

    const inviteUpdate = { ...invite, id: undefined }

    if (invite.acceptLimit) {
      inviteUpdate.count = (invite.count ?? 0) + 1
      if (inviteUpdate.count >= invite.acceptLimit) {
        inviteUpdate.status = "used"
      }
    } else {
      inviteUpdate.status = "used"
      inviteUpdate.accepted = new Date()
    }

    db.update(communityInvitesSchema)
      .set(inviteUpdate)
      .where(eq(communityInvitesSchema.id, invite.id ?? 0))
      .then((re) => re)

    return { ...OK_RESPONSE, communityId: invite.communityId }
  })
