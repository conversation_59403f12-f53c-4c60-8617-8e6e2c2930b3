import { and, eq, or } from "drizzle-orm"

import { db } from "../../../db"
import { communitySchema } from "../../../db/schema/community.schema"
import { userToCommunitySchema } from "../../../db/schema/userToCommunity.schema"
import { hasPermission } from "../../../permissions"
import { protectedProcedure } from "../../../trpc/procedures/protectedProcedure"
import { selectCommunityData } from "../../_selects/select.commuityData"

export const publicCommunityList = protectedProcedure.query(
  async ({ ctx: { loginData } }) => {
    // const imagePath = await communityImagePath()

    return await db
      .select({
        ...selectCommunityData,
      })
      .from(communitySchema)
      .leftJoin(
        userToCommunitySchema,
        and(
          eq(communitySchema.id, userToCommunitySchema.communityId),
          eq(userToCommunitySchema.userId, loginData.id),
        ),
      )
      .where(
        or(
          eq(communitySchema.openness, "public"),
          eq(communitySchema.openness, "publicLimited"),
        ),
      )
      .then(async (communities) => {
        return communities.map((community) => {
          let canSeeLocation = false

          if (
            hasPermission(loginData, "community", "view", {
              id: community.id,
              openness: community.openness,
            })
          ) {
            canSeeLocation = true
          }

          return {
            ...community,
            location: canSeeLocation ? community.location : undefined,
            online: canSeeLocation ? community.online : undefined,
          }
        })
      })
  },
)
