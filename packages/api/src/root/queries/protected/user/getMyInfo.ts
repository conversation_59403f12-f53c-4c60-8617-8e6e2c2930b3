import { eq } from "drizzle-orm"

import { db } from "../../../db"
import { communitySchema } from "../../../db/schema/community.schema"
import { userToCommunitySchema } from "../../../db/schema/userToCommunity.schema"
import { usersSchema } from "../../../db/schema/users.schema"
import { protectedProcedure } from "../../../trpc/procedures/protectedProcedure"
import { tagsAndCats } from "../../_subqueries/game/tagsAndCats"
import { getUserGames } from "../../_subqueries/user/getUserGames"

export const getMyInfo = protectedProcedure.query(
  async ({ ctx: { loginData } }) => {
    const profile = await db
      .select({
        id: usersSchema.id,
        name: usersSchema.name,
        created: usersSchema.created,
        active: usersSchema.active,
        bggUsername: usersSchema.bggUsername,
        color: usersSchema.color,
        email: usersSchema.email,
        avatar: usersSchema.avatar,
        gameCount: usersSchema.gameCount,
      })
      .from(usersSchema)
      .where(eq(usersSchema.id, loginData.id))
      .then((user) => user[0])

    const communities = await db
      .select({
        id: userToCommunitySchema.communityId,
        shareMyGames: userToCommunitySchema.shareMyGames,
        name: communitySchema.name,
        openness: communitySchema.openness,
        memberApproval: communitySchema.memberApproval,
        allowShare: communitySchema.allowShare,
      })
      .from(userToCommunitySchema)
      .innerJoin(
        communitySchema,
        eq(userToCommunitySchema.communityId, communitySchema.id),
      )
      .where(eq(userToCommunitySchema.userId, loginData.id))
      .then((communities) => communities)

    const games = await getUserGames(loginData.id)

    const tags = await tagsAndCats()

    return { profile, communities, games, ...tags }
  },
)
