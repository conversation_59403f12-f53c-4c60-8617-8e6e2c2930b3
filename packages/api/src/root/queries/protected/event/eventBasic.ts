import { TRPCError } from "@trpc/server"
import { and, eq } from "drizzle-orm"
import { z } from "zod"

import { hasPermission } from "../../../../../../common/src/permissions/hasPermissions"
import { db } from "../../../db"
import { communitySchema } from "../../../db/schema/community.schema"
import { communityToEventSchema } from "../../../db/schema/communityToEvent.schema"
import { eventSchema } from "../../../db/schema/event.schema"
import { userToEventSchema } from "../../../db/schema/userToEvent.schema"
import { usersSchema } from "../../../db/schema/users.schema"
import { protectedProcedure } from "../../../trpc/procedures/protectedProcedure"
import { selectEventData } from "../../_selects/select.eventData"
import { selectEvents } from "../../_selects/select.events"
import { hostInfoSchema } from "../../event/eventsListHelper"

export const eventBasic = protectedProcedure
  .input(z.object({ eventId: z.number() }))
  .query(async ({ input, ctx: { loginData } }) => {
    const event = await db
      .select({
        ...selectEventData,
        wizzardState: userToEventSchema.wizzardState,
        userEvent: userToEventSchema.eventId,
      })
      .from(eventSchema)
      .leftJoin(
        hostInfoSchema,
        and(
          eq(hostInfoSchema.subject, "event"),
          eq(hostInfoSchema.roleId, "host"),
          eq(hostInfoSchema.subjectId, selectEvents.id),
        ),
      )
      .leftJoin(usersSchema, eq(hostInfoSchema.userId, usersSchema.id))
      .leftJoin(userToEventSchema, eq(userToEventSchema.userId, loginData.id))
      .where(eq(eventSchema.id, input.eventId))
      .then(async (event) => {
        const communities = await db
          .select({
            id: communitySchema.id,
            name: communitySchema.name,
            image: communitySchema.image,
            owner: communityToEventSchema.owner,
          })
          .from(communityToEventSchema)
          .innerJoin(
            communitySchema,
            eq(communityToEventSchema.communityId, communitySchema.id),
          )
          .where(eq(communityToEventSchema.eventId, event[0].id))
        return { ...event[0], communities, hosts: communities }
      })

    if (!event) {
      throw new TRPCError({
        code: "NOT_FOUND",
        message: "No Event found",
      })
    }

    if (!hasPermission(loginData, "event", "viewPublic", event)) {
      throw new TRPCError({
        code: "FORBIDDEN",
        message: "You can't view this event",
      })
    }

    return {
      ...event,
      isAuthenticated: true,
    }
  })
