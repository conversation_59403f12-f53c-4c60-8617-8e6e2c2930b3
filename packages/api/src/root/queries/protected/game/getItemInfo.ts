import { eq } from "drizzle-orm"
import { z } from "zod"

import { db } from "../../../db"
import { gamesSchema } from "../../../db/schema/games.schema"
import { protectedProcedure } from "../../../trpc/procedures/protectedProcedure"
import { BggGameDataExtracted } from "../../../types/game.types"

export const getItemInfo = protectedProcedure
  .input(z.object({ itemId: z.number() }))
  .query(async ({ input }) => {
    const item = await db
      .select({
        id: gamesSchema.id,
        title: gamesSchema.title,
        bggId: gamesSchema.bggId,
        bggInfo: gamesSchema.bggInfo,
        description: gamesSchema.description,
      })
      .from(gamesSchema)
      .where(eq(gamesSchema.id, input.itemId))
      .then(async (loadGame) => {
        const game = loadGame[0]
        const bggDataExtracted: BggGameDataExtracted = JSON.parse(
          game.bggInfo ?? "{}",
        ) as BggGameDataExtracted

        return {
          id: game.id,
          title: game.title,
          bggId: game.bggId,
          description: game.description,
          players: {
            box: {
              min: parseInt(bggDataExtracted.players.box.min ?? "0"),
              max: parseInt(bggDataExtracted.players.box.max ?? "0"),
            },
            stats: bggDataExtracted.players.stats?.map((stat) => [
              parseInt(stat.players),
              stat.status,
              String(parseInt(stat.players)) != stat.players,
            ]),
          },
          length: {
            box: {
              min: parseInt(bggDataExtracted.playTime.box.min ?? "0"),
              max: parseInt(bggDataExtracted.playTime.box.max ?? "0"),
            },
          },
          age: bggDataExtracted.age,
          average: Math.round(parseFloat(bggDataExtracted.average) * 100) / 100,
          weight: parseInt(bggDataExtracted.averageweight ?? "0"),
          publishYear: bggDataExtracted.publishYear,
          rank: bggDataExtracted.rank,
        }
      })

    return {
      item,
    }
  })
