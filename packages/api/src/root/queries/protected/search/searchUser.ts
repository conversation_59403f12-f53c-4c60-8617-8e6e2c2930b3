import { and, eq, like, or } from "drizzle-orm"
import { z } from "zod"

import { db } from "../../../db"
import { usersSchema } from "../../../db/schema/users.schema"
import { protectedProcedure } from "../../../trpc/procedures/protectedProcedure"

import { SEARCH_ITEM_COUNT } from "./searchConfigs"

export const searchUser = protectedProcedure
  .input(z.object({ search: z.string() }))
  .query(({ input }) => {
    const searchTerm = `%${input.search.trim().substring(0, 20)}%`

    return db
      .selectDistinct({
        id: usersSchema.id,
        name: usersSchema.name,
        avatar: usersSchema.avatar,
        bggUsername: usersSchema.bggUsername,
        color: usersSchema.color,
      })
      .from(usersSchema)
      .where(
        and(
          or(
            like(usersSchema.name, searchTerm),
            like(usersSchema.email, searchTerm),
            like(usersSchema.bggUsername, searchTerm),
          ),
          eq(usersSchema.active, true),
        ),
      )
      .limit(SEARCH_ITEM_COUNT)
  })
