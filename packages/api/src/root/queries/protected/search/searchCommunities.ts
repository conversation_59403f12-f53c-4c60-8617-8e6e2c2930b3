import { and, like } from "drizzle-orm"
import { z } from "zod"

import { db } from "../../../db"
import { communitySchema } from "../../../db/schema/community.schema"
import { protectedProcedure } from "../../../trpc/procedures/protectedProcedure"

import { SEARCH_ITEM_COUNT } from "./searchConfigs"

export const searchCommunities = protectedProcedure
  .input(z.object({ search: z.string() }))
  .query(({ input }) => {
    const searchTerm = `%${input.search.trim().substring(0, 20)}%`

    return db
      .selectDistinct({
        id: communitySchema.id,
        name: communitySchema.name,
        image: communitySchema.image,
        openness: communitySchema.openness,
      })
      .from(communitySchema)
      .where(and(like(communitySchema.name, searchTerm)))
      .limit(SEARCH_ITEM_COUNT)
  })
