import { SQL, and, eq, gt, lt, ne, or } from "drizzle-orm"
import { union } from "drizzle-orm/mysql-core"
import { z } from "zod"

import { hasPermission } from "../../../../../common/src/permissions/hasPermissions"
import { db } from "../../db"
import { communitySchema } from "../../db/schema/community.schema"
import { communityToEventSchema } from "../../db/schema/communityToEvent.schema"
import { eventSchema } from "../../db/schema/event.schema"
import { permissionUserToRoleSchema } from "../../db/schema/permissionUserToRole.schema"
import { communityProcedure } from "../../trpc/procedures/communityProcedure"
import { selectEvents } from "../_selects/select.events"

export const deprecatedCommunityEventsList = communityProcedure
  .input(
    z.object({
      communityId: z.number(),
      period: z.enum(["past", "future", "current", "active"]).optional(),
      owned: z.enum(["host", "my", "all"]).optional(), // "my" = means any event where user has any status
    }),
  )
  .query(async ({ input, ctx: { loginData } }) => {
    let selectPeriod: SQL | undefined

    if (input.period === "past") {
      selectPeriod = and(
        eq(communityToEventSchema.communityId, input.communityId),
        lt(eventSchema.starts, new Date()),
      )
    }

    if (input.period === "future") {
      selectPeriod = and(
        eq(communityToEventSchema.communityId, input.communityId),
        gt(eventSchema.starts, new Date()),
      )
    }

    if (input.period === "current") {
      selectPeriod = and(
        eq(communityToEventSchema.communityId, input.communityId),
        lt(eventSchema.starts, new Date()),
        gt(eventSchema.ends, new Date()),
      )
    }

    if (input.period === undefined || input.period === "active") {
      selectPeriod = or(
        and(
          eq(communityToEventSchema.communityId, input.communityId),
          gt(eventSchema.starts, new Date()),
        ),
        and(
          eq(communityToEventSchema.communityId, input.communityId),
          lt(eventSchema.starts, new Date()),
          gt(eventSchema.ends, new Date()),
        ),
      )
    }

    const selectWhereBase = and(
      eq(communityToEventSchema.communityId, input.communityId),
      ne(eventSchema.state, "hidden"),
    )

    const eventsCommon = db
      .select(selectEvents)
      .from(communityToEventSchema)
      .innerJoin(
        eventSchema,
        eq(communityToEventSchema.eventId, eventSchema.id),
      )
      .leftJoin(
        permissionUserToRoleSchema,
        and(
          eq(permissionUserToRoleSchema.subjectId, eventSchema.id),
          eq(permissionUserToRoleSchema.subject, "event"),
          eq(permissionUserToRoleSchema.userId, loginData.id),
        ),
      )
      .where(and(selectWhereBase, selectPeriod))

    const myHostedEventsWhere = and(
      eq(permissionUserToRoleSchema.subject, "event"),
      eq(permissionUserToRoleSchema.userId, loginData.id),
      or(
        eq(permissionUserToRoleSchema.roleId, "host"),
        eq(permissionUserToRoleSchema.roleId, "cohost"),
      ),
      selectPeriod,
    )

    const myInteractedEventsWhere = and(
      eq(permissionUserToRoleSchema.subject, "event"),
      eq(permissionUserToRoleSchema.userId, loginData.id),
      selectPeriod,
    )

    const myEvents = db
      .select(selectEvents)
      .from(permissionUserToRoleSchema)
      .innerJoin(
        communityToEventSchema,
        and(
          eq(
            communityToEventSchema.eventId,
            permissionUserToRoleSchema.subjectId,
          ),
          eq(communityToEventSchema.communityId, input.communityId),
        ),
      )
      .innerJoin(
        eventSchema,
        eq(communityToEventSchema.eventId, eventSchema.id),
      )
      .where(
        and(
          input.owned === "host"
            ? myHostedEventsWhere
            : myInteractedEventsWhere,
          selectPeriod,
        ),
      )

    const eventsAll = union(eventsCommon, myEvents)

    let eventRequest: typeof myEvents | typeof eventsCommon | typeof eventsAll

    switch (input.owned) {
      case "host":
      case "my":
        eventRequest = myEvents
        break
      case "all":
      default:
        eventRequest = eventsAll
    }

    const events = await eventRequest
      .orderBy(eventSchema.starts)
      .then(async (events) => {
        return await Promise.all(
          events.map(async (event) => {
            const hosts = await db
              .select({
                id: communitySchema.id,
                name: communitySchema.name,
              })
              .from(communityToEventSchema)
              .innerJoin(
                communitySchema,
                and(eq(communityToEventSchema.communityId, communitySchema.id)),
              )
              .where(eq(communityToEventSchema.eventId, event.id))
              .then((hosts) => {
                return hosts
              })

            const eventData = { ...event, communities: hosts, canJoin: false }

            const canView = hasPermission(
              loginData,
              "event",
              "viewPublic",
              eventData,
            )

            const canJoin = hasPermission(loginData, "event", "join", eventData)

            return {
              ...eventData,
              hosts,
              canJoin,
              canView,
            }
          }),
        )
      })

    return events.filter((event) => event.canView)
  })
