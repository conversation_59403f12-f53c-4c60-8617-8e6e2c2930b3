import { TRPCError } from "@trpc/server"
import { and, eq, inArray } from "drizzle-orm"
import { z } from "zod"

import { db } from "../../../../db"
import { communitySchema } from "../../../../db/schema/community.schema"
import { permissionUserToRoleSchema } from "../../../../db/schema/permissionUserToRole.schema"
import { usersSchema } from "../../../../db/schema/users.schema"
import { hasCommunityRole, hasPermission } from "../../../../permissions"
import { communityProcedure } from "../../../../trpc/procedures/communityProcedure"
import { OK_RESPONSE } from "../../../_helpers/responses"

export const changeUserStatus = communityProcedure
  .input(
    z.object({
      communityId: z.number(),
      userId: z.number(),
      status: z.enum(["member", "moder", "banned", "rejected"]),
    }),
  )
  .mutation(async ({ input, ctx: { loginData } }) => {
    if (
      !hasPermission(loginData, "community", "approve", {
        id: input.communityId,
      })
    ) {
      throw new TRPCError({
        code: "FORBIDDEN",
        message: "You don't have permission to approve new members",
      })
    }

    const communityCheck = await db
      .select({
        id: communitySchema.id,
        openness: communitySchema.openness,
        approval: communitySchema.memberApproval,
      })
      .from(communitySchema)
      .where(eq(communitySchema.id, input.communityId))
      .then((community) => community[0])

    if (!communityCheck) {
      throw new TRPCError({
        code: "PRECONDITION_FAILED",
        message: "This community does not exist",
      })
    }

    const userCheck = await db
      .select({
        id: usersSchema.id,
      })
      .from(usersSchema)
      .where(eq(usersSchema.id, input.userId))
      .then((user) => user[0])

    if (!userCheck) {
      throw new TRPCError({
        code: "PRECONDITION_FAILED",
        message: "This user does not exist",
      })
    }

    const existingRoles = await db
      .select({
        id: permissionUserToRoleSchema.id,
        role: permissionUserToRoleSchema.roleId,
        subjectId: permissionUserToRoleSchema.subjectId,
        subject: permissionUserToRoleSchema.subject,
      })
      .from(permissionUserToRoleSchema)
      .where(
        and(
          eq(permissionUserToRoleSchema.userId, input.userId),
          eq(permissionUserToRoleSchema.subjectId, input.communityId),
          eq(permissionUserToRoleSchema.subject, "community"),
        ),
      )
      .then((roles) => roles)

    if (input.status === "rejected") {
      await db
        .delete(permissionUserToRoleSchema)
        .where(
          and(
            eq(permissionUserToRoleSchema.userId, input.userId),
            eq(permissionUserToRoleSchema.subject, "community"),
            eq(permissionUserToRoleSchema.subjectId, input.communityId),
          ),
        )
    }

    if (
      hasCommunityRole(existingRoles, "moder", input.communityId) &&
      !hasPermission(loginData, "community", "promoteModer", {
        id: input.communityId,
      })
    ) {
      throw new TRPCError({
        code: "FORBIDDEN",
        message: "You don't have permission to set or remove moderator status",
      })
    }

    if (hasCommunityRole(existingRoles, "owner", input.communityId)) {
      throw new TRPCError({
        code: "FORBIDDEN",
        message: "You can't change owner status",
      })
    }

    if (input.status === "member" || input.status === "banned") {
      await db
        .delete(permissionUserToRoleSchema)
        .where(
          and(
            eq(permissionUserToRoleSchema.userId, input.userId),
            eq(permissionUserToRoleSchema.subject, "community"),
            eq(permissionUserToRoleSchema.subjectId, input.communityId),
          ),
        )

      await db.insert(permissionUserToRoleSchema).values({
        userId: input.userId,
        roleId: input.status === "member" ? "member" : "banned",
        subject: "community",
        subjectId: communityCheck.id,
      })
    }

    if (
      input.status === "moder" &&
      hasPermission(loginData, "community", "promoteModer", {
        id: input.communityId,
      })
    ) {
      await db
        .delete(permissionUserToRoleSchema)
        .where(
          and(
            inArray(permissionUserToRoleSchema.roleId, ["banned", "invited"]),
            eq(permissionUserToRoleSchema.userId, input.userId),
            eq(permissionUserToRoleSchema.subject, "community"),
            eq(permissionUserToRoleSchema.subjectId, input.communityId),
          ),
        )

      await db.insert(permissionUserToRoleSchema).values({
        userId: input.userId,
        roleId: "moder",
        subject: "community",
        subjectId: communityCheck.id,
      })

      const isMember = db
        .select({
          id: permissionUserToRoleSchema.id,
        })
        .from(permissionUserToRoleSchema)
        .where(
          and(
            eq(permissionUserToRoleSchema.userId, input.userId),
            eq(permissionUserToRoleSchema.subject, "community"),
            eq(permissionUserToRoleSchema.subjectId, input.communityId),
            eq(permissionUserToRoleSchema.roleId, "member"),
          ),
        )
        .then((res) => res.length > 0)

      if (!isMember) {
        await db.insert(permissionUserToRoleSchema).values({
          userId: input.userId,
          roleId: "member",
          subject: "community",
          subjectId: communityCheck.id,
        })
      }
    }

    return OK_RESPONSE
  })
