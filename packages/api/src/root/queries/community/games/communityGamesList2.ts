import { and, desc, eq } from "drizzle-orm"
import { z } from "zod"

import { db } from "../../../db"
import { gameToCommunity2Schema } from "../../../db/schema/gameToCommunity2.schema"
import { gamesSchema } from "../../../db/schema/games.schema"
import { userToCommunitySchema } from "../../../db/schema/userToCommunity.schema"
import { usersSchema } from "../../../db/schema/users.schema"
import { communityProcedure } from "../../../trpc/procedures/communityProcedure"
import { extractGameData, stripExtracted } from "../../_helpers/extractGameData"
import { selectGameData } from "../../_selects/select.gameData"
import { tagsAndCats } from "../../_subqueries/game/tagsAndCats"

export const communityGamesList2 = communityProcedure
  .input(z.object({ communityId: z.number() }))
  .query(async ({ input }) => {
    const games = await db
      .select({
        ...selectGameData,
        tags: gameToCommunity2Schema.tags,
      })
      .from(gameToCommunity2Schema)
      .innerJoin(gamesSchema, eq(gameToCommunity2Schema.gameId, gamesSchema.id))
      .where(
        and(
          eq(gameToCommunity2Schema.communityId, input.communityId ?? 0),
          eq(gamesSchema.type, "base"),
        ),
      )
      .orderBy(desc(gameToCommunity2Schema.news))
      .then((games) => {
        return games.map((game) => {
          const { bggDataExtracted, users, tags } = extractGameData(game)

          return {
            ...game,
            ...stripExtracted,
            players: {
              box: {
                min: parseInt(bggDataExtracted.players.box.min ?? "0"),
                max: parseInt(bggDataExtracted.players.box.max ?? "0"),
              },
              stats: bggDataExtracted.players.stats?.map((stat) => [
                parseInt(stat.players),
                stat.status,
              ]),
            },
            average:
              Math.round(parseFloat(bggDataExtracted.average) * 100) / 100,
            weight: parseInt(bggDataExtracted.averageweight),
            users,
            tags,
          }
        })
      })

    const users = await db
      .select({
        id: usersSchema.id,
        name: usersSchema.name,
        avatar: usersSchema.avatar,
        color: usersSchema.color,
      })
      .from(userToCommunitySchema)
      .innerJoin(usersSchema, eq(userToCommunitySchema.userId, usersSchema.id))
      .where(eq(userToCommunitySchema.communityId, input.communityId ?? 0))
      .then((users) => users)

    const tags = await tagsAndCats()

    return {
      ...tags,
      users,
      games,
    }
  })
