import { and, eq, inArray } from "drizzle-orm"
import { z } from "zod"

import { db } from "../../../db"
import { gameToCommunity2Schema } from "../../../db/schema/gameToCommunity2.schema"
import { gameToExpansionSchema } from "../../../db/schema/gameToExpansion.schema"
import { gameToTagSchema } from "../../../db/schema/gameToTag.schema"
import { gamesSchema } from "../../../db/schema/games.schema"
import { itemToCommunitySchema } from "../../../db/schema/itemToCommunity.schema"
import { tagCategoriesSchema } from "../../../db/schema/tagCategories.schema"
import { tagsSchema } from "../../../db/schema/tags.schema"
import { userToBaseGameSchema } from "../../../db/schema/userToBaseGame.schema"
import { usersSchema } from "../../../db/schema/users.schema"
import { communityProcedure } from "../../../trpc/procedures/communityProcedure"
import { BggGameDataExtracted } from "../../../types/game.types"
import { extractGameData, stripExtracted } from "../../_helpers/extractGameData"

export const communityGameExtended2 = communityProcedure
  .input(z.object({ gameId: z.number(), communityId: z.number() }))
  .query(async ({ input }) => {
    const game = await db
      .select({
        id: gamesSchema.id,
        title: gamesSchema.title,
        bggId: gamesSchema.bggId,
        bggInfo: gamesSchema.bggInfo,
        users: gameToCommunity2Schema.users,
      })
      .from(gamesSchema)
      .innerJoin(
        gameToCommunity2Schema,
        eq(gameToCommunity2Schema.gameId, gamesSchema.id),
      )
      .where(
        and(
          eq(gamesSchema.id, input.gameId),
          eq(gameToCommunity2Schema.communityId, input.communityId),
        ),
      )
      .then(async (loadGame) => {
        const game = loadGame[0]

        const { bggDataExtracted, users: userIds } = extractGameData(game)

        const users = await db
          .select({
            id: usersSchema.id,
            name: usersSchema.name,
            willTeach: userToBaseGameSchema.willTeach,
            experience: userToBaseGameSchema.experience,
            lastPlay: userToBaseGameSchema.lastPlay,
            playCount: userToBaseGameSchema.playCount,
            rating: userToBaseGameSchema.rating,
            color: usersSchema.color,
            avatar: usersSchema.avatar,
            portability: userToBaseGameSchema.portability,
            events: userToBaseGameSchema.events,
            playPriority: userToBaseGameSchema.playPriority,
          })
          .from(usersSchema)
          .leftJoin(
            userToBaseGameSchema,
            and(
              eq(userToBaseGameSchema.userId, usersSchema.id),
              eq(userToBaseGameSchema.gameId, input.gameId),
            ),
          )
          .where(
            inArray(
              usersSchema.id,
              userIds.map((user) => user[0]),
            ),
          )
          .then((users) => users)

        return {
          ...game,
          ...stripExtracted,
          players: {
            box: {
              min: parseInt(bggDataExtracted.players.box.min ?? "0"),
              max: parseInt(bggDataExtracted.players.box.max ?? "0"),
            },
            stats: bggDataExtracted.players.stats?.map((stat) => [
              parseInt(stat.players),
              stat.status,
              parseInt(stat.players).toString() != stat.players,
            ]),
          },
          length: {
            box: {
              min: parseInt(bggDataExtracted.playTime.box.min ?? "0"),
              max: parseInt(bggDataExtracted.playTime.box.max ?? "0"),
            },
          },
          age: bggDataExtracted.age,
          average: Math.round(parseFloat(bggDataExtracted.average) * 100) / 100,
          users,
        }
      })

    const expansions = await db
      .select({
        id: gamesSchema.id,
        title: gamesSchema.title,
        bggId: gamesSchema.bggId,
        bggInfo: gamesSchema.bggInfo,
        users: itemToCommunitySchema.users,
        tags: itemToCommunitySchema.tags,
        news: itemToCommunitySchema.news,
        type: gamesSchema.type,
      })
      .from(gameToExpansionSchema)
      .innerJoin(
        gamesSchema,
        eq(gameToExpansionSchema.expansionId, gamesSchema.id),
      )
      .innerJoin(
        itemToCommunitySchema,
        and(
          eq(gameToExpansionSchema.expansionId, itemToCommunitySchema.gameId),
          eq(itemToCommunitySchema.communityId, input.communityId),
        ),
      )
      .where(eq(gameToExpansionSchema.gameId, input.gameId))
      .then((games) => {
        return games.map((game) => {
          const users: number[] = JSON.parse(game.users ?? "[]") as number[]

          const tags: number[] = JSON.parse(game.tags ?? "[]") as number[]

          const bggDataExtracted: BggGameDataExtracted = JSON.parse(
            game.bggInfo ?? "{}",
          ) as BggGameDataExtracted

          return {
            type: game.type,
            id: game.id,
            title: game.title,
            bggId: game.bggId,
            news: game.news,
            average:
              Math.round(parseFloat(bggDataExtracted.average) * 100) / 100,
            users,
            tags,
          }
        })
      })

    const tags = await db
      .select({
        id: tagsSchema.id,
        type: tagCategoriesSchema.title,
        typeId: tagsSchema.type,
        title: tagsSchema.title,
        color: tagCategoriesSchema.color,
      })
      .from(gameToTagSchema)
      .innerJoin(tagsSchema, eq(gameToTagSchema.tagId, tagsSchema.id))
      .innerJoin(
        tagCategoriesSchema,
        eq(tagsSchema.type, tagCategoriesSchema.id),
      )
      .where(eq(gameToTagSchema.gameId, input.gameId))
      .then((tags) => {
        return tags
      })
    return {
      tags,
      game,
      expansions,
    }
  })
