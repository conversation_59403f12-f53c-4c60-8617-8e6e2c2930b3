import { TRPCError } from "@trpc/server"
import dayjs from "dayjs"
import { eq } from "drizzle-orm"

import { hasPermission } from "../../../../../../common/src/permissions/hasPermissions"
import { createInviteInputs } from "../../../../../../common/src/zodSchemas/postSchemas"
import { db } from "../../../db"
import { communityInvitesSchema } from "../../../db/schema/communityInvites.schema"
import { communityProcedure } from "../../../trpc/procedures/communityProcedure"
import { OK_RESPONSE } from "../../_helpers/responses"

function makeid(length: number): string {
  let result = ""
  const characters =
    "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789"
  const charactersLength = characters.length
  let counter = 0
  while (counter < length) {
    result += characters.charAt(Math.floor(Math.random() * charactersLength))
    counter += 1
  }
  return result
}

export const createInvite = communityProcedure
  .input(createInviteInputs)
  .mutation(async ({ input, ctx: { loginData } }) => {
    if (
      !hasPermission(loginData, "community", "invite", {
        id: input.communityId,
      })
    ) {
      throw new TRPCError({
        code: "FORBIDDEN",
        message: "You can't create new invite",
      })
    }

    let code = ""
    let alreadyExists: number | null = 1

    while (alreadyExists && alreadyExists > 0) {
      code = `${Date.now().toString(36)}${makeid(10)}`
      alreadyExists = await db
        .select({ id: communityInvitesSchema.id })
        .from(communityInvitesSchema)
        .where(eq(communityInvitesSchema.inviteString, code))
        .then((invite) => invite[0]?.id ?? null)
    }

    await db
      .insert(communityInvitesSchema)
      .values({
        userId: null,
        communityId: input.communityId,
        inviteString: code,
        expiration: input.expiration ? dayjs(input.expiration).toDate() : null,
        status: "sent",
        acceptLimit: input.count ?? null,
        count: 0,
        inviterId: loginData.id,
        accepted: null,
      })
      .then((re) => re)

    const link = code

    return {
      ...OK_RESPONSE,
      link: `${process.env.MAIN_HOST}/join?code=${link}`,
    }
  })
