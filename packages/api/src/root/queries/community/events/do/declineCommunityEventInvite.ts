import { TRPCError } from "@trpc/server"
import { and, eq } from "drizzle-orm"
import { z } from "zod"

import { hasPermission } from "../../../../../../../common/src/permissions/hasPermissions"
import { db } from "../../../../db"
import { communityToEventSchema } from "../../../../db/schema/communityToEvent.schema"
import { communityProcedure } from "../../../../trpc/procedures/communityProcedure"
import { OK_RESPONSE } from "../../../_helpers/responses"

export const declineCommunityEventInvite = communityProcedure
  .input(z.object({ eventId: z.number(), communityId: z.number() }))
  .mutation(async ({ input, ctx: { loginData, community } }) => {
    if (
      !hasPermission(loginData, "community", "event", {
        id: community.id,
      })
    ) {
      throw new TRPCError({
        code: "FORBIDDEN",
        message: "You don't have permission to view invites",
      })
    }

    if (
      !hasPermission(loginData, "community", "update", {
        id: community.id,
      })
    ) {
      throw new TRPCError({
        code: "FORBIDDEN",
        message: "You don't have permission to view invites",
      })
    }

    const invite = await db
      .select({ eventId: communityToEventSchema.eventId })
      .from(communityToEventSchema)
      .where(
        and(
          eq(communityToEventSchema.eventId, input.eventId),
          eq(communityToEventSchema.communityId, community.id),
          eq(communityToEventSchema.invite, true),
        ),
      )
      .then((invite) => invite[0])

    if (!invite) {
      throw new TRPCError({
        code: "NOT_FOUND",
        message: "Invite not found",
      })
    }

    await db
      .delete(communityToEventSchema)
      .where(
        and(
          eq(communityToEventSchema.eventId, input.eventId),
          eq(communityToEventSchema.communityId, community.id),
        ),
      )
      .then((re) => re)

    return { ...OK_RESPONSE }
  })
