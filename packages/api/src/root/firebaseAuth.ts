import express from "express"
import admin from "firebase-admin"

admin.initializeApp({
  credential: admin.credential.cert(
    "./root/explain-games-firebase-sa-key.json",
  ),
})

export const firebaseAuth = (): express.Handler => {
  return (
    req: express.Request,
    res: express.Response,
    next: express.NextFunction,
  ) => {
    const token = req.headers.authorization?.replace("Bearer ", "")
    if (!token) {
      next()
      return
    }

    admin
      .auth()
      .verifyIdToken(token)
      .then((decodedToken) => {
        res.locals.auth = {
          payload: { ...decodedToken, sub: decodedToken.uid },
        }
        next()
      })
      .catch((error: unknown) => {
        // For public endpoints, we should continue without authentication
        // rather than returning an error
        console.warn("Firebase token verification failed:", error)
        next()
      })
  }
}

export const firebaseLogout = async (userId: string) => {
  try {
    await admin.auth().revokeRefreshTokens(userId)
  } catch (error: unknown) {
    console.error(error)
  }
}
