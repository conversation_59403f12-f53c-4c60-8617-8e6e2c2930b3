import { Box, Grid, Typography } from "@mui/material"

import { GameInfoRow } from "../../../../components/games/GameInfoRow/GameInfoRow"
import { utcToLocal } from "../../../../utils/transformTime"
import * as styles from "../gamePage.module.css"

import type { IUserProfileGameData } from "../../../../types/tRPC.types"

interface OwnerInfoProps {
  userSelected: IUserProfileGameData
  id?: number
}
export const OwnerInfo = ({ userSelected }: OwnerInfoProps) => {
  return (
    <>
      <Typography variant="subtitle1" color="textSecondary">
        {"My info"}
      </Typography>
      <Box className={styles.infoRowCell}>
        {userSelected !== null && (
          <Grid container spacing={2} columns={2}>
            {(userSelected.rating ?? 0) > 0 && (
              <GameInfoRow title="Rating" value={String(userSelected.rating)} />
            )}
            {userSelected.lastPlay && (
              <GameInfoRow
                title="Last Play"
                value={utcToLocal(userSelected.lastPlay)}
              />
            )}
            {(userSelected.playCount ?? 0) > 0 && (
              <GameInfoRow
                title="Play count"
                value={String(userSelected.playCount ?? 0)}
              />
            )}
            <GameInfoRow
              title="Can teach"
              value={userSelected.willTeach ? "Yes" : "No"}
            />
            {userSelected.portability && (
              <GameInfoRow
                title="Portability"
                value={userSelected.portability}
              />
            )}
            {userSelected.events && (
              <GameInfoRow title="Events" value={userSelected.events} />
            )}
          </Grid>
        )}
      </Box>
    </>
  )
}
