import { Box } from "@mui/material"

import { EVENT_ROOT_ROUTE } from "../../../routes/paths"
import { useUserStore } from "../../../store/useUserStore"
import { isEvent, useParentRouteData } from "../../../utils/pages.rootObject"

import { GameList } from "./components/GameList"
import { Header } from "./components/Header"
import { UserList } from "./components/UserList"
import { Welcome } from "./components/Welcome"

export const EventInfoPage = () => {
  const event = useParentRouteData(EVENT_ROOT_ROUTE)
  const { userData } = useUserStore((state) => state)

  const user = userData.id ? userData : null

  if (!event || !isEvent(event)) {
    return null
  }

  const isWelcome = (event.description?.length ?? 0) > 7

  return (
    <Box pb={8}>
      <Header />
      {isWelcome && (
        <Box>
          <Welcome />
        </Box>
      )}
      <Box gap={4} display="flex" flexDirection="column">
        <GameList />
        {user && <UserList />}
      </Box>
    </Box>
  )
}
