import { Box, Typography } from "@mui/material"
import useEmblaCarousel from "embla-carousel-react"

import { PartyLink } from "../../../../components/elements/link/PartyLink/PartyLink"
import { UserRow } from "../../../../components/user/UserRow/UserRow"
import { displayUsersCount } from "../../../../config/community.conf"
import { eventInfoRoute } from "../../../../routes/event/eventInfo.route"
import { EVENT_PARTICIPANTS_ROUTE } from "../../../../routes/paths"

import * as styles from "./userList.module.css"

export const UserList = () => {
  const event = eventInfoRoute.useLoaderData()
  const linkParams = eventInfoRoute.useParams()

  const [emblaRef] = useEmblaCarousel({
    axis: "x",
    dragFree: true,
    skipSnaps: true,
  })

  if (!event || !event.participants) {
    return null
  }

  return (
    <Box width="100%" overflow="hidden" id="members">
      <Box className={styles.gridTitle} pl={2} pr={2}>
        <Typography variant="h5">Participants</Typography>
        <PartyLink
          to={EVENT_PARTICIPANTS_ROUTE}
          params={{ eventId: linkParams.eventId }}
        >
          See all participants
        </PartyLink>
      </Box>
      <Box overflow="hidden" width="100%" ref={emblaRef}>
        <Box display="flex" gap={2}>
          {event.participants.slice(0, displayUsersCount).map((user) => (
            <UserRow
              labelInfo="Participant: "
              key={user.id}
              user={user}
              eventId={Number(linkParams.eventId)}
            />
          ))}
        </Box>
      </Box>
    </Box>
  )
}
