import { Box, Typography } from "@mui/material"
import { useNavigate } from "@tanstack/react-router"
import useEmblaCarousel from "embla-carousel-react"
import { useCallback } from "react"

import { SkipLink } from "../../../../components/elements/SkipLink/SkipLink"
import { PartyLink } from "../../../../components/elements/link/PartyLink/PartyLink"
import { eventInfoRoute } from "../../../../routes/event/eventInfo.route"
import {
  COMMUNITY_USER_ROUTE,
  EVENT_GAMES_ROUTE,
  EVENT_GAME_ROUTE,
  EVENT_ROOT_ROUTE,
} from "../../../../routes/paths"
import { useIsMobileStore } from "../../../../store/useIsMobileStore"
import { isEvent, useParentRouteData } from "../../../../utils/pages.rootObject"

import { GameThumbnailWrapper } from "./GameThumbnailWrapper"
import * as styles from "./gameList.module.css"

export const GameList = () => {
  const sizeThresholdList = useIsMobileStore((state) => state.sizeThresholdList)
  const event = eventInfoRoute.useLoaderData()
  const linkParams = eventInfoRoute.useParams()
  const navigate = useNavigate()
  const base = useParentRouteData(EVENT_ROOT_ROUTE)
  const [emblaRef] = useEmblaCarousel({
    axis: "x",
    dragFree: true,
    skipSnaps: true,
  })

  if (!base || !isEvent(base)) {
    return null
  }

  const onClick = useCallback(
    (id?: number) =>
      navigate({
        to: COMMUNITY_USER_ROUTE,
        params: {
          userId: String(id),
          communityId: String(base?.id ?? 0),
        },
      }),
    [navigate, base?.id],
  )

  const isLargeTablet = sizeThresholdList.largeTablet

  const displayUsersCount = isLargeTablet ? 9 : 20

  return (
    <Box width="100%" overflow="hidden" id="games">
      <SkipLink goTo="#members" title="Skip to members" />

      <Box className={styles.gridTitle}>
        <Typography variant="h5">Games in event</Typography>
        {base.isAuthenticated && (
          <PartyLink
            to={EVENT_GAMES_ROUTE}
            params={{ eventId: linkParams.eventId }}
          >
            See all games
          </PartyLink>
        )}
      </Box>

      <Box overflow="hidden" width="100%" height="260px" ref={emblaRef}>
        <Box display="flex" gap={2}>
          {event?.games.slice(0, displayUsersCount).map((game) => (
            <GameThumbnailWrapper
              navigation={
                base.isAuthenticated
                  ? {
                      to: EVENT_GAME_ROUTE,
                      params: {
                        gameId: String(game.id),
                        eventId: String(linkParams.eventId),
                      },
                      search: {
                        sourcePage: "eventInfo",
                      },
                    }
                  : undefined
              }
              onClick={onClick}
              lookupUsers={event.participants ?? []}
              game={game}
              key={game.id}
            />
          ))}
        </Box>
      </Box>
    </Box>
  )
}
