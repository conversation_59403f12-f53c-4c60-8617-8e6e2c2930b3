import { useMemo } from "react"

import { GameThumbnail } from "../../../../components/games/GameThumbnail/GameThumbnail"
import {
  type ICNavigationProps,
  type ICThumbnailGame,
} from "../../../../components/games/GameThumbnail/type"
import {
  type ICAvatarUser,
  type ICFAvatarOnClick,
} from "../../../../components/user/UserAvatar/UserAvatar"
import { isNewEntry } from "../../../../utils/transformTime"

import type { GameUserTuple } from "../../../../types/types"

export type ICCEventThumbnailGame = ICThumbnailGame & {
  news: string | null
  users: GameUserTuple[]
}

interface GameThumbnailProps {
  game: ICCEventThumbnailGame
  onClick: ICFAvatarOnClick
  lookupUsers: ICAvatarUser[]
  navigation?: ICNavigationProps
}
export const GameThumbnailWrapper = ({
  game,
  lookupUsers,
  onClick,
  navigation,
}: GameThumbnailProps) => {
  const isNew = !isNewEntry(game.news)

  const userList = useMemo(() => {
    return game.users
      ? game.users
          .map((user) => lookupUsers.find((look) => look.id == user[0]))
          .filter((user) => user !== undefined)
      : []
  }, [game.users, lookupUsers])

  return (
    <GameThumbnail
      navigation={navigation}
      game={game}
      onUser={onClick}
      userList={userList}
      isNew={isNew}
    />
  )
}
