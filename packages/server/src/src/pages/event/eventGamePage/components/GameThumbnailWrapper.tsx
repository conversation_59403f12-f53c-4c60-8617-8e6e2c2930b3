import classnames from "classnames"
import { useMemo } from "react"

import { GameListItem } from "../../../../components/games/GameListItem/GameListItem"
import { GameThumbnail } from "../../../../components/games/GameThumbnail/GameThumbnail"
import { type ICAvatarUser } from "../../../../components/user/UserAvatar/UserAvatar"
import { eventGameRoute } from "../../../../routes/event/eventGame.route"
import { useIsMobileStore } from "../../../../store/useIsMobileStore"
import { isNewEntry } from "../../../../utils/transformTime"

import * as styles from "./expansionThumbnail.module.css"

import type { IEventGameViewExpansion } from "../../../../types/tRPC.types"

interface ExpansionThumbnailProps {
  expansion: IEventGameViewExpansion
  onViewUser: (user?: number) => void
  onOpenItem: (id: number) => void
  users: ICAvatarUser[]
}
export const GameThumbnailWrapper = ({
  expansion,
  onViewUser,
  onOpenItem,
  users,
}: ExpansionThumbnailProps) => {
  const { userId } = eventGameRoute.useSearch()

  const hasOwner = useMemo(() => {
    let hasOwnerL = !userId
    expansion.users?.forEach((user) => {
      if (user === userId) {
        hasOwnerL = true
      }
    })

    return hasOwnerL
  }, [expansion.users, userId])

  const userList = useMemo(() => {
    return expansion.users
      .map((user: number) => {
        return users.find((gUser) => gUser.id === user)
      })
      .filter((user) => user !== undefined)
  }, [expansion.users, users])

  const isNew = !isNewEntry(expansion.news)

  const sizeThresholdList = useIsMobileStore((state) => state.sizeThresholdList)

  const isLargeTablet = sizeThresholdList.largeTablet

  const Component = isLargeTablet ? GameListItem : GameThumbnail

  return (
    <Component
      userList={userList}
      className={classnames(styles.card, {
        [styles.hasOwner]: hasOwner,
      })}
      isBase={expansion.type === "base" || expansion.type === "base-expansion"}
      isExpansion={
        expansion.type !== "base" && expansion.type !== "base-expansion"
      }
      onClick={onOpenItem}
      onUser={onViewUser}
      game={expansion}
      isNew={isNew}
    />
  )
}
