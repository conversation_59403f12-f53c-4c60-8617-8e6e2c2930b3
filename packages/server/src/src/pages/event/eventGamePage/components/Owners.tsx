import {
  Box,
  Button,
  Checkbox,
  FormControlLabel,
  Typo<PERSON>,
} from "@mui/material"
import { useNavigate } from "@tanstack/react-router"
import { useCallback } from "react"

import { LabelTooltip } from "../../../../components/elements/LabelTooltip/LabelTooltip"
import {
  OWNERS_MODAL_NAME,
  OwnersModal,
  useModalStore,
} from "../../../../components/modals"
import { OwnersList } from "../../../../components/user/OwnersList/OwnersList"
import { eventGameRoute } from "../../../../routes/event/eventGame.route"
import {
  EVENT_GAME_ROUTE,
  EVENT_PARTICIPANT_ROUTE,
} from "../../../../routes/paths"
import { useUserStore } from "../../../../store/useUserStore"
import { GameUserSort, ownerSort } from "../../../../utils/ownerSort"

import * as styles from "./owners.module.css"

const MAX_DISPLAY_USERS = 3

type GameUser = {} & GameUserSort

interface OwnerListProps {
  search: {
    hideOthers?: boolean
    userId?: number
  }
  users: GameUser[]
  onViewUser: (user?: number) => void
  eventId: number
}

export const Owners = ({
  search,
  users,
  onViewUser,
  eventId,
}: OwnerListProps) => {
  const navigate = useNavigate()
  const params = eventGameRoute.useParams()

  const myUserId = useUserStore((state) => state.userData.id)
  const extraUsers = users.length - MAX_DISPLAY_USERS

  const { openModal } = useModalStore()

  const onChange = useCallback(
    () =>
      navigate({
        to: EVENT_GAME_ROUTE,
        params: params,
        search: {
          userId: search.userId,
          hideOthers: !search.hideOthers,
        },
      }),
    [navigate, search, params],
  )

  const onOpenUserProfile = useCallback(
    (id: number) =>
      navigate({
        to: EVENT_PARTICIPANT_ROUTE,
        params: {
          participantId: String(id),
          eventId: String(eventId),
        },
      }),
    [navigate, eventId],
  )

  const list = users.sort(ownerSort(myUserId ?? 0)).slice(0, MAX_DISPLAY_USERS)

  return (
    <Box display="flex" flexDirection="column">
      <Box>
        <Box
          gap={0.5}
          pb={1}
          display="flex"
          flexDirection="column"
          flexWrap="wrap"
        >
          <OwnersList
            showActive={false}
            userList={list}
            onViewUser={onViewUser}
            onOpenUserProfile={onOpenUserProfile}
            filterUserId={search.userId}
          />
          {extraUsers > 0 && (
            <Box className={styles.userRow}>
              <Button
                title={`Open list of all owners`}
                variant="text"
                onClick={() =>
                  openModal(OWNERS_MODAL_NAME, { userId: search.userId })
                }
              >
                {`show all owners (+${extraUsers})`}
              </Button>
            </Box>
          )}
        </Box>
      </Box>
      {list.length > 1 && (
        <Box className={styles.selectContainer}>
          <FormControlLabel
            control={
              <Checkbox checked={!!search.hideOthers} onChange={onChange} />
            }
            label="Show selected only"
          />
          <LabelTooltip>
            <Typography variant="body2">If user is selected:</Typography>
            <Typography variant="body2">
              If checked - only show games owned by selected user.
            </Typography>
            <Typography variant="body2">
              If unchecked - only highlight selected users games.
            </Typography>
          </LabelTooltip>
        </Box>
      )}
      <OwnersModal
        filterUserId={search.userId}
        userList={users}
        onViewUser={onViewUser}
        onOpenUserProfile={onOpenUserProfile}
      />
    </Box>
  )
}
