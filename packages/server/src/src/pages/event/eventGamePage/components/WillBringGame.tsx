import { Box, Icon, Typography } from "@mui/material"
import { useRouter } from "@tanstack/react-router"
import { useCallback } from "react"

import { hasPermission } from "../../../../../../../common/src/permissions/hasPermissions"
import { GameStatusButtons } from "../../../../components/events/GameStatusButtons/GameStatusButtons"
import { userGamaStateStrings } from "../../../../config/userGameStateStrings"
import { eventGameRoute } from "../../../../routes/event/eventGame.route"
import { EVENT_ROOT_ROUTE } from "../../../../routes/paths"
import { useToastStore } from "../../../../store/useToastStore"
import { useUserStore } from "../../../../store/useUserStore"
import { trpc } from "../../../../trpc/trpc"
import { EventGameStatus, EventGameStatusReset } from "../../../../types/types"
import { isEvent, useParentRouteData } from "../../../../utils/pages.rootObject"

export const WillBringGame = () => {
  const game = eventGameRoute.useLoaderData()
  const event = useParentRouteData(EVENT_ROOT_ROUTE)
  const user = useUserStore((state) => state.userData)
  const params = eventGameRoute.useParams()
  const router = useRouter()
  const { setTrpcError, setMessage } = useToastStore()

  const handleOnStatusChange = useCallback(
    async (status: EventGameStatusReset) => {
      if (!game) {
        return
      }
      try {
        await trpc.event.games.do.updateStatus.mutate({
          eventId: Number(params.eventId),
          gameId: game.game.id,
          status,
        })
        router.invalidate()
        setMessage({
          message:
            "Game status updated. Remember: it will take few minutes for changes in event game list.",
          code: "DONE",
          severity: "success",
        })
      } catch (error: unknown) {
        setTrpcError(error)
      }
    },
    [params.eventId, game, setMessage, setTrpcError, router],
  )

  if (!event || !isEvent(event) || !game) return null

  if (!event.share) {
    return null
  }

  const canShare = hasPermission(user, "event", "shareUserGames", event)
  if (!canShare) return null

  let myEventStatus: EventGameStatus | null = null

  if (game.game.eventStatus) {
    myEventStatus =
      game.game.eventStatus ??
      (game.game.canRequest ? "canask" : "willnotbring")
  }

  const globalEventStatus =
    myEventStatus === "willbring"
      ? "willbring"
      : game.game.users.reduce((acc: EventGameStatus, { eventStatus }) => {
          const status = eventStatus ?? "willnotbring"
          return userGamaStateStrings[status].weight <
            userGamaStateStrings[acc].weight
            ? status
            : acc
        }, myEventStatus ?? "willnotbring")

  return (
    <Box display="flex" flexDirection="column" gap={2}>
      {myEventStatus !== "willbring" && (
        <Box display="flex" flexDirection="row" gap={1} alignItems="center">
          <Icon>{userGamaStateStrings[globalEventStatus].icon}</Icon>
          <Typography
            variant="h6"
            color={userGamaStateStrings[globalEventStatus].color}
          >
            {userGamaStateStrings[globalEventStatus].title}
          </Typography>
        </Box>
      )}
      {myEventStatus === null ? (
        <Box display="flex" flexDirection="column" gap={2}>
          {!game.game.userOwns && (
            <Typography variant="h6" color="info">
              You don't own this game
            </Typography>
          )}
          <GameStatusButtons
            onClick={handleOnStatusChange}
            status={myEventStatus}
            own={!!game.game.userOwns}
          />
        </Box>
      ) : (
        <Box display="flex" flexDirection="column" gap={2}>
          {(game.game.userOwns ||
            myEventStatus === "wish" ||
            myEventStatus === "willplay" ||
            myEventStatus === "maybeplay") && (
            <Box display="flex" flexDirection="row" gap={1} alignItems="center">
              <Icon>{userGamaStateStrings[myEventStatus].icon}</Icon>
              <Typography
                variant="h6"
                color={userGamaStateStrings[myEventStatus].color}
              >
                {userGamaStateStrings[myEventStatus].titleMy}
              </Typography>
            </Box>
          )}
          <GameStatusButtons
            onClick={handleOnStatusChange}
            status={myEventStatus}
            own={!!game.game.userOwns}
          />
        </Box>
      )}
    </Box>
  )
}
