import { useNavigate } from "@tanstack/react-router"

import { ChipsList } from "../../../../components/games/ChipsList/ChipsList"
import { eventGameRoute } from "../../../../routes/event/eventGame.route"
import { EVENT_GAMES_ROUTE } from "../../../../routes/paths"

import type { IGameViewTag } from "../../../../types/tRPC.types"

interface ChipListProps {
  tagList: IGameViewTag[]
}

export const Chips = ({ tagList }: ChipListProps) => {
  const navigate = useNavigate()
  const linkParams = eventGameRoute.useParams()

  const openTag = (tag: IGameViewTag) =>
    navigate({
      to: EVENT_GAMES_ROUTE,
      params: {
        eventId: linkParams.eventId,
      },
      search: {
        search: tag.title,
        orderBy: "asc",
        page: 1,
        order: "search",
      },
    })

  return <ChipsList tagList={tagList} onOpenTag={openTag} />
}
