@value isSmallDesktop, isTablet from "../../../css/size.module.css";

.imageBox {
  max-height: 250px;
  width: 250px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.borderBox {
  border-top: 1px solid #ccc;
}

.link {
  position: absolute;
  top: var(--spacing-1);
  right: var(--spacing-1);
}

.bggRating {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  pointer-events: none;
}

.firstRowWrapper {
  display: flex;
  flex-direction: row;
  gap: 16px;
  flex-wrap: wrap;
  justify-content: space-between;
  padding-top: 32px;
  row-gap: 48px;
}

.gameInfoWrapper {
  display: flex;
  flex-direction: row;
  gap: 16px;
  flex-wrap: wrap;
  row-gap: 48px;
}

@media (max-width: isSmallDesktop) {
  .firstRowWrapper {
    flex-direction: column;
  }
}

@media (max-width: isTablet) {
  .gameInfoWrapper {
    flex-direction: column;
  }
}