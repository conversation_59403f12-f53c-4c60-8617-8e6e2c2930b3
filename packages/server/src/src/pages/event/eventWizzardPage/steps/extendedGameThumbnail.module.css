.radioGroup {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: center;
  box-sizing: border-box;
}

.container {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
  align-items: center;
  justify-content: center;
  width: 100%;
background-color: #eee;
border-bottom-left-radius:  var(--spacing-2);
border-bottom-right-radius: var(--spacing-2);
    transition: opacity 0.2s ease-in-out,
    box-shadow 0.2s ease-in-out;
}

.containerNo {
    opacity: 0.3;
}

.containerNo:hover {
    opacity: 1;
}