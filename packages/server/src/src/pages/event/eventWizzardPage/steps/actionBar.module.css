.container {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: rgba(255, 255, 255, 0.7);
    border-top: 1px solid #ccc;
    z-index: 1000;
    transition: opacity 0.2s ease-in-out,
    box-shadow 0.2s ease-in-out;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
  align-items: center;
  justify-content: center;
  width: 100%;
  padding: var(--spacing-4);
  box-sizing: border-box;
}
