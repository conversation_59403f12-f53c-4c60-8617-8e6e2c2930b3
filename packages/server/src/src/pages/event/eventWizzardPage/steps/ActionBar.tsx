import { Box } from "@mui/material"
import { useNavigate, useRouter } from "@tanstack/react-router"
import { useCallback } from "react"

import { LoadingButtonWithError } from "../../../../components/elements/LoadingButtonWithError/LoadingButtonWithError"
import { EVENT_GAMES_ROUTE } from "../../../../routes/paths"
import { useToastStore } from "../../../../store/useToastStore"
import { trpc } from "../../../../trpc/trpc"

import * as styles from "./actionBar.module.css"

type NavigateProps = {
  eventId: number
  noPrevious?: boolean
  presetId?: number | null
  isDone?: boolean
}

export const ActionBar = ({
  eventId,
  noPrevious = false,
  presetId,
  isDone = false,
}: NavigateProps) => {
  const router = useRouter()
  const navigate = useNavigate()
  const { setTrpcError } = useToastStore()

  const nextStep = useCallback(async () => {
    try {
      await trpc.event.wizzard.do.updateSettingsExtended.mutate({
        eventId,
        presetId,
      })

      if (isDone) {
        navigate({
          to: EVENT_GAMES_ROUTE,
          params: {
            eventId: String(eventId),
          },
        })
      } else {
        router.invalidate()
      }
    } catch (error: unknown) {
      setTrpcError(error)
    }
  }, [eventId, isDone, presetId])

  const prevStep = useCallback(async () => {
    try {
      await trpc.event.wizzard.do.updateSettingsExtended.mutate({
        eventId,
        prevStep: true,
      })
      router.invalidate()
    } catch (error: unknown) {
      setTrpcError(error)
    }
  }, [eventId])

  return (
    <Box className={styles.container}>
      <Box display="flex" justifyContent="flex-end" gap={4}>
        {!noPrevious && (
          <LoadingButtonWithError
            title="Back"
            variant="contained"
            color="secondary"
            onClick={prevStep}
          />
        )}
        <LoadingButtonWithError
          title={isDone ? "Done" : "Next"}
          variant="contained"
          onClick={nextStep}
        />
      </Box>
    </Box>
  )
}
