import { Box } from "@mui/material"
import { use<PERSON><PERSON><PERSON>, useRouter } from "@tanstack/react-router"
import classNames from "classnames"
import { useCallback, useMemo } from "react"

import {
  GameSearch,
  type SearchParams,
} from "../../../../components/games/GameSearch/GameSearch"
import { GameThumbnail } from "../../../../components/games/GameThumbnail/GameThumbnail"
import { Pagination } from "../../../../components/games/GamesThumbnailView/Pagination"
import { SearchModal } from "../../../../components/modals"
import { GAMES_PER_PAGE } from "../../../../config/game.conf"
import { eventWizzardRoute } from "../../../../routes/event/eventWizzard.route"
import {
  EVENT_GAME_ROUTE,
  EVENT_ROOT_ROUTE,
  EVENT_WIZZARD_ROUTE,
} from "../../../../routes/paths"
import { useToastStore } from "../../../../store/useToastStore"
import { trpc } from "../../../../trpc/trpc"
import { EventGameStatus } from "../../../../types/types"
import { applyFilters } from "../../../../utils/filter"
import { isEvent, useParentRouteData } from "../../../../utils/pages.rootObject"

import { ActionBar } from "./ActionBar"
import { ExtendedGameThumbnail } from "./ExtendedGameThumbnail"
import * as styles from "./games.module.css"

import type { ICTag } from "../../../../components/games/GameSearch/components/ChipList"

export const Games = () => {
  const event = useParentRouteData(EVENT_ROOT_ROUTE)
  const settings = eventWizzardRoute.useLoaderData()
  const data = eventWizzardRoute.useLoaderData()
  const router = useRouter()
  const search = eventWizzardRoute.useSearch()
  const params = eventWizzardRoute.useParams()
  const navigate = useNavigate()
  const { setTrpcError } = useToastStore()

  const onChange = useCallback(
    (_: React.ChangeEvent<unknown>, page: number) => {
      navigate({
        to: EVENT_WIZZARD_ROUTE,
        params,
        search: {
          ...search,
          page,
        },
      })

      window.scrollTo({ top: 0 })
    },
    [navigate, params, search],
  )

  const onNavigate = useCallback(
    (search: SearchParams) =>
      navigate({
        to: EVENT_WIZZARD_ROUTE,
        params,
        search: {
          ...search,
        },
      }),
    [navigate, params],
  )

  if (!event || !isEvent(event) || !data) {
    return null
  }

  const { games, tags, tagCategories } = data

  const optimizeTags = useMemo(() => {
    const populatedTags: ICTag[][] = []

    games.forEach((currentGame) => {
      populatedTags[currentGame.id] = currentGame.tags!.map(
        (currentTag) => (tags ?? []).find((tag) => tag.id === currentTag)!,
      )
    })

    return populatedTags
  }, [tags])

  const useGameList = useMemo(() => {
    return applyFilters(games, search, optimizeTags, undefined, undefined, {
      canAsk: data.canRequest,
    })
  }, [games, search])

  const pages = Math.ceil(useGameList.length / GAMES_PER_PAGE)
  const startItem = GAMES_PER_PAGE * ((search.page ?? 1) - 1)
  const endItem = startItem + GAMES_PER_PAGE

  const navParams = {
    eventId: String(params.eventId),
  }

  const pagination = useMemo(
    () => (
      <Pagination pages={pages} page={search.page ?? 1} onChange={onChange} />
    ),
    [pages, onChange, search],
  )

  const handleChangeGameStatus = useCallback(
    async (gameId: number, status: string) => {
      try {
        await trpc.event.games.do.updateStatus.mutate({
          eventId: event.id,
          gameId,
          status: status as EventGameStatus,
        })
        router.invalidate()
      } catch (error: unknown) {
        setTrpcError(error)
      }
    },
    [event.id],
  )

  return (
    <Box
      display="flex"
      gap={4}
      flexWrap="wrap"
      justifyContent="center"
      alignItems="center"
      flexDirection="column"
      width="100%"
      padding={2}
      pb={8}
      boxSizing="border-box"
    >
      <GameSearch
        search={search}
        onNavigate={onNavigate}
        personalOrder
        tags={tags}
        tagCategories={tagCategories}
        enableFilters="eventWizard"
      />
      <SearchModal
        search={search}
        onNavigate={onNavigate}
        personalOrder
        tags={tags}
        tagCategories={tagCategories}
      />
      <Box
        display="flex"
        justifyContent="center"
        flexDirection="column"
        gap={4}
        pt={2}
        pb={8}
      >
        {pagination}
        <Box display="flex" gap={2} flexWrap="wrap" justifyContent="center">
          {useGameList.slice(startItem, endItem).map((game) => (
            <Box key={game.id}>
              <GameThumbnail
                navigation={{
                  to: EVENT_GAME_ROUTE,
                  search: {
                    sourcePage: "eventWizzard",
                    sourceProps: JSON.stringify(search),
                    sourceParams: JSON.stringify(navParams),
                  },
                  params: {
                    eventId: String(event.id),
                    gameId: String(game.id),
                  },
                }}
                game={game}
                key={game.id}
                containerClassName={classNames(styles.gameThumbnail, {
                  [styles.gameThumbnailYes]: game.eventStatus === "willbring",
                  [styles.gameThumbnailNo]:
                    game.eventStatus === "willnotbring" ||
                    (game.eventStatus === null && !settings?.canRequest),
                })}
              />
              <ExtendedGameThumbnail
                game={game}
                canRequest={settings?.canRequest ?? true}
                onChange={handleChangeGameStatus}
              />
            </Box>
          ))}
        </Box>
        {pagination}
      </Box>
      <ActionBar eventId={event.id} />
    </Box>
  )
}
