import {
  Accordion,
  Accordion<PERSON>um<PERSON>y,
  Box,
  Button,
  Typography,
} from "@mui/material"
import { useMemo, useState } from "react"

import { GameSmallThumbnail } from "../../../../components/games/GameSmallThumbnail/GameSmallThumbnail"
import {
  EVENT_PRESET_MODAL_NAME,
  EventPresetModal,
  useModalStore,
} from "../../../../components/modals"
import { eventWizzardRoute } from "../../../../routes/event/eventWizzard.route"
import { EVENT_ROOT_ROUTE } from "../../../../routes/paths"
import { isEvent, useParentRouteData } from "../../../../utils/pages.rootObject"

import { ActionBar } from "./ActionBar"

export const Review = () => {
  const event = useParentRouteData(EVENT_ROOT_ROUTE)
  const data = eventWizzardRoute.useLoaderData()
  const { openModal } = useModalStore()
  const [openedPanel, setOpenedPanel] = useState<
    "willbring" | "wish" | "willplay" | null | "canask" | "willnotbring"
  >("willbring")

  if (!event || !isEvent(event) || !data) {
    return null
  }

  const { games } = data

  const useGamesWillBring = useMemo(() => {
    return games.filter((game) => game.eventStatus === "willbring")
  }, [games])

  const useGamesWish = useMemo(() => {
    return games.filter((game) => game.eventStatus === "wish")
  }, [games])

  const useGamesWillPlay = useMemo(() => {
    return games.filter((game) => game.eventStatus === "willplay")
  }, [games])

  return (
    <Box pb={12}>
      <Accordion
        expanded={openedPanel === "willbring"}
        onChange={() => setOpenedPanel("willbring")}
      >
        <AccordionSummary aria-controls="panel1d-content" id="panel1d-header">
          <Typography component="span">
            Games I will bring to the event
          </Typography>
        </AccordionSummary>
        <Box
          display="flex"
          gap={2}
          flexWrap="wrap"
          justifyContent="center"
          p={2}
        >
          {useGamesWillBring.map((game) => (
            <GameSmallThumbnail game={game} onClick={() => {}} key={game.id} />
          ))}
        </Box>
        <Box display="flex" justifyContent="flex-end" p={2}>
          <Button
            variant="contained"
            color="primary"
            onClick={() =>
              openModal(EVENT_PRESET_MODAL_NAME, {
                eventId: event.id,
              })
            }
          >
            Save as preset
          </Button>
        </Box>
      </Accordion>

      {useGamesWish.length > 0 && (
        <Accordion
          expanded={openedPanel === "wish"}
          onChange={() => setOpenedPanel("wish")}
        >
          <AccordionSummary aria-controls="panel1d-content" id="panel1d-header">
            <Typography component="span">
              Games I want to play, but no one has said they will bring
            </Typography>
          </AccordionSummary>
          <Box
            display="flex"
            gap={2}
            flexWrap="wrap"
            justifyContent="center"
            p={2}
          >
            {useGamesWish.map((game) => (
              <GameSmallThumbnail
                game={game}
                onClick={() => {}}
                key={game.id}
              />
            ))}
          </Box>
        </Accordion>
      )}

      {useGamesWillPlay.length > 0 && (
        <Accordion
          expanded={openedPanel === "willplay"}
          onChange={() => setOpenedPanel("willplay")}
        >
          <AccordionSummary aria-controls="panel1d-content" id="panel1d-header">
            <Typography component="span">
              Games I want to play, and someone has said they will bring
            </Typography>
          </AccordionSummary>
          <Box
            display="flex"
            gap={2}
            flexWrap="wrap"
            justifyContent="center"
            p={2}
          >
            {useGamesWillPlay.map((game) => (
              <GameSmallThumbnail
                game={game}
                onClick={() => {}}
                key={game.id}
              />
            ))}
          </Box>
        </Accordion>
      )}

      <ActionBar eventId={event.id} isDone />
      <EventPresetModal />
    </Box>
  )
}
