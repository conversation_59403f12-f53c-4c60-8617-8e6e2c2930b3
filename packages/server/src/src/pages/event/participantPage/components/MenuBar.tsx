import CasinoIcon from "@mui/icons-material/Casino"
import PermIdentityIcon from "@mui/icons-material/PermIdentity"
import { Tab, Tabs } from "@mui/material"
import { useNavigate } from "@tanstack/react-router"
import { SyntheticEvent, useCallback } from "react"

import { eventParticipantRoute } from "../../../../routes/event/eventParticipant.route"
import { EVENT_PARTICIPANT_ROUTE } from "../../../../routes/paths"

interface MenuBarProps {
  eventId: number
  participantId: number
}
export const MenuBar = ({ eventId, participantId }: MenuBarProps) => {
  const search = eventParticipantRoute.useSearch()
  const navigate = useNavigate()
  const handleChange = useCallback(
    (_: SyntheticEvent<Element, Event>, value: string) => {
      navigate({
        to: EVENT_PARTICIPANT_ROUTE,
        params: {
          eventId: String(eventId),
          participantId: String(participantId),
        },
        search: {
          tab: value,
        },
      })
    },
    [navigate, eventId, participantId],
  )

  return (
    <Tabs value={search.tab ?? "profile"} onChange={handleChange}>
      <Tab
        value="profile"
        tabIndex={0}
        icon={<PermIdentityIcon />}
        label="Profile"
      />
      <Tab value="games" tabIndex={0} icon={<CasinoIcon />} label="Games" />
    </Tabs>
  )
}
