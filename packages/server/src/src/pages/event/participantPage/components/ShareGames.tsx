import { Box, FormControlLabel, Switch, Typography } from "@mui/material"
import React, { useCallback } from "react"

import {
  LoaderDialog,
  type LoaderDialogState,
} from "../../../../components/common/LoaderDialog/LoaderDialog"
import { useToastStore } from "../../../../store/useToastStore"
import { trpc } from "../../../../trpc/trpc"

interface ShareGamesProps {
  share: boolean
  communityId: number
  userId: number
}
export const ShareGames = ({ share, userId, communityId }: ShareGamesProps) => {
  const [loading, setLoading] = React.useState<LoaderDialogState>(null)
  const { setTrpcError } = useToastStore()
  const onChange = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      setLoading("loading")
      const value = event.target.checked
      trpc.community.user.do.updateGameShare
        .mutate({
          communityId,
          userId,
          share: value,
        })
        .then(() => {
          setLoading(null)
        })
        .catch((error: unknown) => {
          setTrpcError(error)
          setLoading(null)
        })
    },
    [userId, communityId],
  )

  return (
    <>
      <FormControlLabel
        control={<Switch onChange={onChange} defaultChecked={share} />}
        label="Share games in this community"
      />
      <Box>
        <Typography variant="caption" color="textSecondary">
          If this is turned on - all games will be visible for other users in
          this community.
        </Typography>
      </Box>
      <LoaderDialog
        state={loading}
        onClose={() => setLoading(null)}
        failMessage={"Failed to change share setting"}
      />
    </>
  )
}
