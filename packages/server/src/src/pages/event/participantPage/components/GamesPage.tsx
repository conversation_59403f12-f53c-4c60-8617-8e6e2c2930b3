import { Box } from "@mui/material"
import { useN<PERSON><PERSON>, useRouter } from "@tanstack/react-router"
import { useCallback, useMemo } from "react"

import { TitleRow } from "../../../../components/common/TitleRow/TitleRow"
import {
  GameSearch,
  SearchParams,
} from "../../../../components/games/GameSearch/GameSearch"
import { GamesThumbnailView } from "../../../../components/games/GamesThumbnailView/GamesThumbnailView"
import { SearchModal } from "../../../../components/modals"
import { eventParticipantRoute } from "../../../../routes/event/eventParticipant.route"
import {
  EVENT_GAME_ROUTE,
  EVENT_PARTICIPANT_ROUTE,
  EVENT_ROOT_ROUTE,
} from "../../../../routes/paths"
import { useIsMobileStore } from "../../../../store/useIsMobileStore"
import { useToastStore } from "../../../../store/useToastStore"
import { trpc } from "../../../../trpc/trpc"
import { EventGameStatusReset } from "../../../../types/types"
import { applyFilters } from "../../../../utils/filter"
import { isEvent, useParentRouteData } from "../../../../utils/pages.rootObject"

import * as styles from "./gamesPage.module.css"

import type { ICTag } from "../../../../components/games/GameSearch/components/ChipList"

export const GamesPage = () => {
  const event = useParentRouteData(EVENT_ROOT_ROUTE)
  const navigate = useNavigate({ from: EVENT_PARTICIPANT_ROUTE })
  const sizeThresholdList = useIsMobileStore((state) => state.sizeThresholdList)
  const search = eventParticipantRoute.useSearch()
  const linkParams = eventParticipantRoute.useParams()
  const data = eventParticipantRoute.useLoaderData()
  const router = useRouter()
  const { setTrpcError, setMessage } = useToastStore()

  const games = data && data.games
  const tags = data && data.tags
  const tagCategories = data && data.tagCategories

  const handleOnStatusChange = useCallback(
    async (status: EventGameStatusReset, gameId: number) => {
      try {
        await trpc.event.games.do.updateStatus.mutate({
          eventId: Number(linkParams.eventId),
          gameId,
          status,
        })
        router.invalidate()
        setMessage({
          message:
            "Game status updated. Remember: it will take few minutes for changes in event game list.",
          code: "DONE",
          severity: "success",
        })
      } catch (error: unknown) {
        setTrpcError(error)
      }
    },
    [linkParams.eventId, setMessage, setTrpcError, router],
  )

  if (!isEvent(event) || !games) {
    return null
  }

  const optimizeTags = useMemo(() => {
    const populatedTags: ICTag[][] = []

    games.forEach((currentGame) => {
      populatedTags[currentGame.id] = currentGame.tags!.map(
        (currentTag) => (tags ?? []).find((tag) => tag.id === currentTag)!,
      )
    })

    return populatedTags
  }, [tags])

  const useGameList = useMemo(() => {
    return applyFilters(
      games,
      search,
      optimizeTags,
      undefined,
      "event-willbring",
    )
  }, [games, search])

  const onNavigateGame = useCallback(
    (search: SearchParams) => {
      navigate({
        to: EVENT_PARTICIPANT_ROUTE,
        search: { tab: "games", ...search },
        params: linkParams,
      })
    },
    [navigate, linkParams],
  )

  const onChange = useCallback(
    (page: number) => {
      navigate({
        search: {
          page,
          minPlayers: search.minPlayers,
          maxPlayers: search.maxPlayers,
          playerLevel: search.playerLevel,
          search: search.search ?? "",
          order: search.order,
          orderBy: search.orderBy,
          filter: search.filter,
          tab: "games",
        },
      })

      window.scrollTo({ top: 0 })
    },
    [search, navigate],
  )

  const titleRow = useMemo(() => {
    return <TitleRow title="Event Games" />
  }, [linkParams.eventId])

  const isLargeTablet = sizeThresholdList.largeTablet

  return (
    <>
      <Box className={styles.header}>
        <Box className={styles.searchBar}>
          <GameSearch
            onNavigate={onNavigateGame}
            search={search}
            tags={tags}
            tagCategories={tagCategories}
            enableFilters="event"
          />
          <SearchModal
            onNavigate={onNavigateGame}
            search={search}
            tags={tags}
            tagCategories={tagCategories}
          />
        </Box>
        {titleRow}
      </Box>
      <GamesThumbnailView
        listMode={isLargeTablet}
        navigation={{
          to: EVENT_GAME_ROUTE,
          params: {
            eventId: linkParams.eventId.toString(),
          },
          search: {
            sourcePage: "eventParticipant",
            sourceProps: JSON.stringify(search),
            sourceParams: JSON.stringify({
              eventId: linkParams.eventId.toString(),
              participantId: linkParams.participantId.toString(),
            }),
          },
        }}
        type="event"
        games={useGameList}
        onPageChange={onChange}
        page={search.page ?? 1}
        onEventStatusChange={handleOnStatusChange}
      />
    </>
  )
}
