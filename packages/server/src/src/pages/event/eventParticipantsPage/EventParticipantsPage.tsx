import { Button } from "@mui/material"

import { TitleRow } from "../../../components/common/TitleRow/TitleRow"
import {
  ADD_PARTICIPANT_MODAL_NAME,
  AddParticipantModal,
} from "../../../components/modals/AddParticipantModal/AddParticipantModal"
import { UserList } from "../../../components/user/UserList/UserList"
import { eventParticipantsRoute } from "../../../routes/event/eventParticipants.route"
import { EVENT_ROOT_ROUTE } from "../../../routes/paths"
import { useModalStore } from "../../../store/useModalStore"
import { isEvent, useParentRouteData } from "../../../utils/pages.rootObject"

export const EventParticipantsPage = () => {
  const event = useParentRouteData(EVENT_ROOT_ROUTE)
  const participants = eventParticipantsRoute.useLoaderData()
  const { openModal } = useModalStore()

  if (!event || !isEvent(event) || !participants) {
    return null
  }

  return (
    <>
      <TitleRow title="Participants">
        <Button
          variant="outlined"
          onClick={() => openModal(ADD_PARTICIPANT_MODAL_NAME)}
        >
          Add participant
        </Button>
      </TitleRow>
      <UserList
        users={participants}
        eventId={event.id}
        labelInfo="Participant: "
      />
      <AddParticipantModal eventId={event.id} />
    </>
  )
}
