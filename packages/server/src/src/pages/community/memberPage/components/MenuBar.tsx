import CasinoIcon from "@mui/icons-material/Casino"
import PermIdentityIcon from "@mui/icons-material/PermIdentity"
import { Tab, Tabs } from "@mui/material"
import { useNavigate } from "@tanstack/react-router"
import { SyntheticEvent, useCallback } from "react"

import { memberRoute } from "../../../../routes/community/member.route"
import { COMMUNITY_USER_ROUTE } from "../../../../routes/paths"

interface MenuBarProps {
  communityId: number
  userId: number
}
export const MenuBar = ({ communityId, userId }: MenuBarProps) => {
  const search = memberRoute.useSearch()
  const navigate = useNavigate()
  const handleChange = useCallback(
    (_: SyntheticEvent<Element, Event>, value: string) => {
      navigate({
        to: COMMUNITY_USER_ROUTE,
        params: {
          communityId: String(communityId),
          userId: String(userId),
        },
        search: {
          tab: value,
        },
      })
    },
    [navigate, communityId, userId],
  )

  return (
    <Tabs value={search.tab ?? "games"} onChange={handleChange}>
      <Tab value="games" tabIndex={0} icon={<CasinoIcon />} label="Games" />
      <Tab
        value="profile"
        tabIndex={0}
        icon={<PermIdentityIcon />}
        label="Profile"
      />
    </Tabs>
  )
}
