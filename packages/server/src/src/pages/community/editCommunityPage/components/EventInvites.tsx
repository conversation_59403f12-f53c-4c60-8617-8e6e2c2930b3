import { Box, Typography } from "@mui/material"
import { useRouter } from "@tanstack/react-router"
import { useCallback } from "react"

import { EventThumbnail } from "../../../../components/events/EventThumbnail/EventThumbnail"
import { communityEditRoute } from "../../../../routes/community/communityEdit.route"
import { COMMUNITIES_ROOT_ROUTE } from "../../../../routes/paths"
import { useToastStore } from "../../../../store/useToastStore"
import { trpc } from "../../../../trpc/trpc"
import {
  isCommunity,
  useParentRouteData,
} from "../../../../utils/pages.rootObject"

export const EventInvites = () => {
  const community = useParentRouteData(COMMUNITIES_ROOT_ROUTE)
  const router = useRouter()
  const { setTrpcError } = useToastStore()
  const invites = communityEditRoute.useLoaderData()

  const handleConfirmInvite = useCallback(async (eventId: number) => {
    if (!community || !isCommunity(community) || !community.id) {
      return
    }
    try {
      await trpc.community.events.do.confirmInvite.mutate({
        eventId,
        communityId: community.id,
      })
      router.invalidate()
    } catch (error: unknown) {
      setTrpcError(error)
    }
  }, [])

  const handleDeclineInvite = useCallback(async (eventId: number) => {
    if (!community || !isCommunity(community) || !community.id) {
      return
    }
    try {
      await trpc.community.events.do.declineInvite.mutate({
        eventId,
        communityId: community.id,
      })
      router.invalidate()
    } catch (error: unknown) {
      setTrpcError(error)
    }
  }, [])

  if (!community || !isCommunity(community) || !community.id) {
    return null
  }

  return (
    <Box>
      {invites &&
        invites.map((invite) => (
          <EventThumbnail
            event={invite}
            invite
            onAcceptInvite={handleConfirmInvite}
            onDeclineInvite={handleDeclineInvite}
          />
        ))}
      {invites?.length === 0 && (
        <Box>
          <Typography variant="h6">No invites</Typography>
        </Box>
      )}
    </Box>
  )
}
