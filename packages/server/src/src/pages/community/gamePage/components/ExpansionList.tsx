import { Box, FormControl, TextField, Typography } from "@mui/material"
import { useNavigate } from "@tanstack/react-router"
import debounce from "debounce"
import {
  type ChangeEvent,
  type KeyboardEvent,
  useCallback,
  useMemo,
  useState,
} from "react"

import {
  ITEM_INFO_MODAL_NAME,
  ItemInfoModal,
  useModalStore,
} from "../../../../components/modals"
import { type ICAvatarUser } from "../../../../components/user/UserAvatar/UserAvatar"
import { gameRoute } from "../../../../routes/community/game.route"
import { GAME_ROUTE } from "../../../../routes/paths"
import { useIsMobileStore } from "../../../../store/useIsMobileStore"

import { GameThumbnailWrapper } from "./GameThumbnailWrapper"

import type {
  IGameViewExpansion,
  IGameViewExpansions,
} from "../../../../types/tRPC.types"

interface ExpansionListProps {
  expansions: IGameViewExpansions
  users: ICAvatarUser[]
  onViewUser: (user?: number) => void
}

const sort = (s1: IGameViewExpansion, s2: IGameViewExpansion) => {
  if (
    (s1.type === "base" || s1.type === "base-expansion") &&
    s2.type !== "base" &&
    s2.type !== "base-expansion"
  ) {
    return -1
  }

  if (
    (s2.type === "base" || s2.type === "base-expansion") &&
    s1.type !== "base" &&
    s1.type !== "base-expansion"
  ) {
    return 1
  }
  return s1.bggId > s2.bggId ? 1 : -1
}

export const ExpansionList = ({
  expansions,
  onViewUser,
  users,
}: ExpansionListProps) => {
  const [openItem, setOpenItem] = useState<number | null>(null)
  const { userId, hideOthers, search } = gameRoute.useSearch()
  const navigate = useNavigate()
  const linkParams = gameRoute.useParams()
  const [searchText, setSearchText] = useState(search)
  const { openModal } = useModalStore()
  const sizeThresholdList = useIsMobileStore((state) => state.sizeThresholdList)

  const isLargeTablet = sizeThresholdList.largeTablet

  const check = userId && hideOthers

  const onSearch = useCallback(
    (search: string) => {
      navigate({
        to: GAME_ROUTE,
        params: {
          communityId: linkParams.communityId,
          gameId: linkParams.gameId,
        },
        search: {
          userId: userId,
          hideOthers: hideOthers,
          search: search,
        },
      })
    },
    [linkParams, userId, hideOthers],
  )

  const onSearchDelayed = useMemo(() => debounce(onSearch, 100), [onSearch])

  const onOpenItem = useCallback(
    (id: number) => {
      setOpenItem(id)
      openModal(ITEM_INFO_MODAL_NAME)
    },
    [setOpenItem, openModal],
  )

  const onSearchType = (event: ChangeEvent<HTMLInputElement>) => {
    onSearchDelayed(event.target.value)
    setSearchText(event.target.value)
  }

  const onKeyDown = (e: KeyboardEvent<HTMLDivElement>) => {
    if (e.key === "Escape") {
      setSearchText("")
      onSearch("")
    }
  }

  const searchLower = search?.toLowerCase() ?? ""

  return (
    <Box>
      <Box mb={2} display="flex" justifyContent="space-between">
        <Typography variant="h6">Owned game items</Typography>
        <FormControl>
          <TextField
            label="Search"
            variant="outlined"
            value={searchText}
            onChange={onSearchType}
            onKeyDown={onKeyDown}
          />
        </FormControl>
      </Box>
      <Box
        display="flex"
        gap={2}
        flexWrap="wrap"
        justifyContent="center"
        rowGap={isLargeTablet ? 0 : 4}
      >
        {expansions
          .sort(sort)
          .filter((expansion) =>
            expansion.title.toLowerCase().includes(searchLower),
          )
          .filter((expansion) =>
            check ? expansion.users?.find((user) => user == userId) : true,
          )
          .map((expansion: IGameViewExpansion) => (
            <GameThumbnailWrapper
              users={users}
              onViewUser={onViewUser}
              onOpenItem={onOpenItem}
              key={expansion.id}
              expansion={expansion}
            />
          ))}
      </Box>
      <ItemInfoModal itemId={openItem} />
    </Box>
  )
}
