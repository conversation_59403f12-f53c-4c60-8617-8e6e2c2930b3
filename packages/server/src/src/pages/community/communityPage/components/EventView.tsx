import { Box, Card, CardContent, CardMedia, Typography } from "@mui/material"
import classnames from "classnames"
import useEmblaCarousel from "embla-carousel-react"

import { SkipLink } from "../../../../components/elements/SkipLink/SkipLink"
import { PartyLink } from "../../../../components/elements/link/PartyLink/PartyLink"
import { createImageLink } from "../../../../config/images"
import {
  COMMUNITY_EVENTS_ROUTE,
  EVENT_ROUTE_INFO,
} from "../../../../routes/paths"
import { localToLocalWithDay } from "../../../../utils/transformTime"

import * as styles from "./eventView.module.css"

interface Event {
  id: number
  title: string
  starts: string
  image: string
}
interface EventViewProps {
  events: Event[]
  communityId: string
}
export const EventView = ({ events, communityId }: EventViewProps) => {
  const [emblaRef] = useEmblaCarousel({
    axis: "x",
    dragFree: true,
    skipSnaps: true,
  })

  return (
    <Box>
      <SkipLink goTo="#games" title="Skip to games" />
      <Box className={styles.gridTitle}>
        <Typography variant="h5">Upcoming Events</Typography>
        <PartyLink
          to={COMMUNITY_EVENTS_ROUTE}
          params={{ communityId: communityId }}
        >
          See all events
        </PartyLink>
      </Box>
      <Box overflow="hidden" width="100%" height="260px" ref={emblaRef}>
        <Box display="flex" gap={2}>
          {events.map((event) => (
            <PartyLink
              className={styles.cardLink}
              to={EVENT_ROUTE_INFO}
              key={event.id}
              params={{ eventId: String(event.id) }}
            >
              <Card className={classnames(styles.card)}>
                <CardMedia
                  component="img"
                  className={styles.image}
                  src={createImageLink("event", "large", event.id, event.image)}
                />
                <CardContent>
                  <Typography variant="subtitle1" className={styles.title}>
                    {event.title}
                  </Typography>
                  <Typography variant="h6">
                    {localToLocalWithDay(event.starts, true)}
                  </Typography>
                </CardContent>
              </Card>
            </PartyLink>
          ))}
        </Box>
      </Box>
    </Box>
  )
}
