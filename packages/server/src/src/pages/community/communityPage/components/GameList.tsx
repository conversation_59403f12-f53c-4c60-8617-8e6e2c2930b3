import { Box, Typography } from "@mui/material"
import { useNavigate } from "@tanstack/react-router"
import useEmblaCarousel from "embla-carousel-react"
import { useCallback } from "react"

import { SkipLink } from "../../../../components/elements/SkipLink/SkipLink"
import { PartyLink } from "../../../../components/elements/link/PartyLink/PartyLink"
import { communityOpenRoute } from "../../../../routes/community/community.route"
import {
  COMMUNITIES_ROOT_ROUTE,
  COMMUNITY_USER_ROUTE,
  GAMES_ROUTE,
  GAME_ROUTE,
} from "../../../../routes/paths"
import { useIsMobileStore } from "../../../../store/useIsMobileStore"
import {
  isCommunity,
  useParentRouteData,
} from "../../../../utils/pages.rootObject"
import * as styles from "../community.module.css"

import { GameThumbnailWrapper } from "./GameThumbnailWrapper"

export const GameList = () => {
  const sizeThresholdList = useIsMobileStore((state) => state.sizeThresholdList)
  const community = communityOpenRoute.useLoaderData()
  const linkParams = communityOpenRoute.useParams()
  const navigate = useNavigate()
  const base = useParentRouteData(COMMUNITIES_ROOT_ROUTE)

  if (!base || !isCommunity(base)) {
    return null
  }

  const [emblaRef] = useEmblaCarousel({
    axis: "x",
    dragFree: true,
    skipSnaps: true,
  })

  const onClick = useCallback(
    (id?: number) =>
      navigate({
        to: COMMUNITY_USER_ROUTE,
        params: {
          userId: String(id),
          communityId: String(base?.id ?? 0),
        },
      }),
    [navigate, base?.id],
  )

  const isLargeTablet = sizeThresholdList.largeTablet

  const displayUsersCount = isLargeTablet ? 9 : 20

  return (
    <Box width="100%" overflow="hidden" id="games">
      <SkipLink goTo="#members" title="Skip to members" />
      <Box className={styles.gridTitle}>
        <Typography variant="h5">New games</Typography>
        <PartyLink
          to={GAMES_ROUTE}
          params={{ communityId: linkParams.communityId }}
        >
          See all games
        </PartyLink>
      </Box>
      <Box overflow="hidden" width="100%" height="260px" ref={emblaRef}>
        <Box display="flex" gap={2}>
          {community?.games.games.slice(0, displayUsersCount).map((game) => (
            <GameThumbnailWrapper
              navigation={{
                to: GAME_ROUTE,
                params: {
                  gameId: String(game.id),
                  communityId: String(linkParams.communityId),
                },
                search: {
                  sourcePage: "community",
                },
              }}
              onClick={onClick}
              lookupUsers={community?.games.users ?? []}
              game={game}
              communityId={Number(linkParams.communityId)}
              key={game.id}
            />
          ))}
        </Box>
      </Box>
    </Box>
  )
}
