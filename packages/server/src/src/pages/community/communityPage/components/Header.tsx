import CardMembershipIcon from "@mui/icons-material/CardMembership"
import HourglassEmptyIcon from "@mui/icons-material/HourglassEmpty"
import LanguageIcon from "@mui/icons-material/Language"
import MapIcon from "@mui/icons-material/Map"
import { LoadingButton } from "@mui/lab"
import { Alert, Box, Button, Link, Typography } from "@mui/material"
import { useCallback, useState } from "react"

import { LoaderDialog } from "../../../../components/common/LoaderDialog/LoaderDialog"
import { PartyLink } from "../../../../components/elements/link/PartyLink/PartyLink"
import {
  ALLOW_SHARE_MODAL_NAME,
  AllowShareModal,
  INVITE_MODAL_NAME,
  InviteModal,
  useModalStore,
} from "../../../../components/modals"
import { hasCommunityRole, hasPermission } from "../../../../permissions"
import { communityOpenRoute } from "../../../../routes/community/community.route"
import {
  COMMUNITIES_ROOT_ROUTE,
  COMMUNITY_EVENT_ROUTE,
} from "../../../../routes/paths"
import { useToastStore } from "../../../../store/useToastStore"
import { useUserStore } from "../../../../store/useUserStore"
import { trpc } from "../../../../trpc/trpc"
import {
  isCommunity,
  useParentRouteData,
} from "../../../../utils/pages.rootObject"

import * as styles from "./header.module.css"

export const Header = () => {
  const community = communityOpenRoute.useLoaderData()
  const base = useParentRouteData(COMMUNITIES_ROOT_ROUTE)
  const userInfo = useUserStore((state) => state.userData)
  const { setTrpcError } = useToastStore()

  const [sendingJoin, setSendingJoin] = useState<boolean>(false)
  const [receivedJoinSent, setReceivedJoinSent] = useState<boolean>(false)
  const [doShare, setDoShare] = useState<boolean>(false)
  const [failedJoin, setFailedJoin] = useState<boolean>(false)
  const { openModal, closeModal } = useModalStore()

  if (!isCommunity(base) || !community || !userInfo) {
    return null
  }

  const onJoinDo = useCallback(async () => {
    closeModal(ALLOW_SHARE_MODAL_NAME)
    setSendingJoin(true)
    trpc.protected.community.do.join
      .mutate({
        communityId: base.id,
        shareMyGames: doShare,
      })
      .then((response) => {
        if (response.success) {
          setReceivedJoinSent(true)
        }
        setSendingJoin(false)
      })
      .catch((error: unknown) => {
        console.error("Join failed:", error)
        setTrpcError(error)
        setSendingJoin(false)
      })
  }, [
    trpc,
    base.id,
    doShare,
    closeModal,
    setReceivedJoinSent,
    setSendingJoin,
    setFailedJoin,
  ])

  const onJoin = useCallback(() => {
    if (!base?.share) {
      openModal(ALLOW_SHARE_MODAL_NAME)
    } else {
      onJoinDo()
    }
  }, [trpc, base?.id, openModal])

  const isMember = hasPermission(userInfo, "community", "isMember", {
    id: base.id,
  })

  const isInvited = hasCommunityRole(userInfo.roles, "invited", base.id)

  return (
    <>
      <Box className={styles.title}>
        <Box className={styles.titleBox}>
          <Box className={styles.itemsBox}>
            {isMember && (
              <Box className={styles.membershipStatus}>
                <Alert
                  icon={<CardMembershipIcon fontSize="medium" />}
                  severity="info"
                >
                  <Typography color="info">Member</Typography>
                </Alert>
              </Box>
            )}
            {((receivedJoinSent && base.approval) || isInvited) && (
              <Box className={styles.membershipStatus}>
                <Alert
                  icon={<HourglassEmptyIcon fontSize="inherit" />}
                  severity="success"
                >
                  Joining: Waiting for approve
                </Alert>
              </Box>
            )}
            <Typography variant="body2" fontWeight="bold">
              {base.openness.toUpperCase()}
            </Typography>
            {base.location && (
              <Box className={styles.location} title={base.location}>
                <MapIcon />
                <Typography variant="body1">{base.location}</Typography>{" "}
              </Box>
            )}
            {base.online && (
              <Box className={styles.location} title={base.online}>
                <LanguageIcon />
                <Link
                  href={base.online}
                  underline="none"
                  variant="body1"
                  target="_blank"
                >
                  In Web
                </Link>{" "}
              </Box>
            )}
          </Box>
          <Box display="flex" flexDirection="row" gap={2} alignItems="center">
            {!receivedJoinSent &&
              !isMember &&
              !isInvited &&
              base.openness === "public" && (
                <Box className={styles.membershipStatus}>
                  <LoadingButton
                    loadingPosition="start"
                    loading={sendingJoin}
                    variant="contained"
                    onClick={onJoin}
                  >{`${base.approval ? "Request to" : ""} Join`}</LoadingButton>
                </Box>
              )}
            {hasPermission(userInfo, "community", "invite", base) && (
              <Box>
                {
                  <Button
                    onClick={() => openModal(INVITE_MODAL_NAME)}
                    variant="outlined"
                  >
                    Create Invite
                  </Button>
                }
              </Box>
            )}
            {hasPermission(userInfo, "community", "event", base) && (
              <Box>
                <PartyLink
                  to={COMMUNITY_EVENT_ROUTE}
                  params={{ communityId: String(base.id) }}
                  variant="outlined"
                >
                  Create Event
                </PartyLink>
              </Box>
            )}
          </Box>
        </Box>
      </Box>
      <AllowShareModal onChange={setDoShare} onContinue={onJoinDo} />
      <LoaderDialog
        state={failedJoin ? "failed" : null}
        title="Failed to send Join request"
        onClose={() => {
          setFailedJoin(false)
        }}
      />
      <InviteModal communityId={base.id} />
    </>
  )
}
