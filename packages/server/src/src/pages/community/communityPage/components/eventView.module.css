.cardLink {
    width: 350px;
    min-width: 350px;
    position: relative;
    display: block;
}

.card {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  cursor: pointer;
    width: 350px;
    min-width: 350px;
    position: relative;
}

.image {
    height: 150px;
    object-fit: cover;
    width: 100%;
}

.participant {
  background-color: #eeeeee;
}

.host {
  background-color: #dddddd;
}

.title {
    line-clamp: 2;
    overflow: hidden;
    -webkit-line-clamp: 2;
    display: -webkit-box;
    -webkit-box-orient: vertical;
}

.gridTitle {
    display: flex;
    flex-direction: row;
    width: 100%;
    justify-content: space-between;
    margin-bottom: var(--spacing-2);
}