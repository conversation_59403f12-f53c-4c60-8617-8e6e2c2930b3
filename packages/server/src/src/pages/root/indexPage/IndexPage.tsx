import { Alert, Box, Typography } from "@mui/material"

import { LoginButton } from "../../../components/common/LoginButton/LoginButton"
import { PartyLink } from "../../../components/elements/link/PartyLink/PartyLink"
import { hasPermission } from "../../../permissions"
import {
  COMMUNITY_OPEN_ROUTE,
  CREATE_COMMUNITY_ROUTE,
  EVENTS_ROUTE,
  PROFILE_ROUTE,
  PUBLIC_COMMUNITIES_ROUTE,
} from "../../../routes/paths"
import { useUserStore } from "../../../store/useUserStore"

export const IndexPage = () => {
  const { isLoggedIn, userData } = useUserStore()

  return (
    <Box
      alignContent="center"
      justifyContent="center"
      display="flex"
      flexDirection="column"
      padding={5}
    >
      <Box
        alignContent="center"
        justifyContent="center"
        display="flex"
        flexDirection="column"
        gap={2}
      >
        <Typography variant="h5">Welcome to the Party App!</Typography>
        <Typography variant="body1">
          This app is meant to create groups for boradgamers to share their
          games with friends and/or local communities.
        </Typography>
        {!isLoggedIn && (
          <Alert severity="warning" title="Login">
            To start exploring please <LoginButton /> first!
          </Alert>
        )}
        {isLoggedIn && (
          <Box display="flex" flexDirection="column" gap={2}>
            <Typography variant="body1">
              View all public communities:{" "}
              <PartyLink to={PUBLIC_COMMUNITIES_ROUTE} variant="contained">
                Public communities
              </PartyLink>
            </Typography>
            <Typography variant="body1">
              View all communities that I have joined or created:{" "}
              <PartyLink to={COMMUNITY_OPEN_ROUTE} variant="contained">
                My communities
              </PartyLink>
            </Typography>
            <Typography variant="body1">
              Look at current events:{" "}
              <PartyLink to={EVENTS_ROUTE} variant="contained">
                Events
              </PartyLink>
            </Typography>
            <Typography variant="body1">
              Edit my general information:{" "}
              <PartyLink to={PROFILE_ROUTE} variant="contained">
                Profile
              </PartyLink>
            </Typography>
            {hasPermission(userData, "global", "createCommunity", {
              createdCommunities: userData.countCommunities,
            }) ? (
              <Typography variant="body1" fontWeight="bold">
                Create new community:{" "}
                <PartyLink to={CREATE_COMMUNITY_ROUTE} variant="text">
                  Create community
                </PartyLink>
              </Typography>
            ) : (
              <Typography variant="body1">
                To create Your own community You need to gain permission from
                site administration for now.
              </Typography>
            )}
          </Box>
        )}
      </Box>
    </Box>
  )
}
