import { createRoute } from "@tanstack/react-router"

import { CHANGING_LISTS_STALE_TIME } from "../../config/routes"
import { LoadingPage } from "../../pages/common/LoadingPage/LoadingPage"
import { PrivateEventPage } from "../../pages/index/privateEventPage/PrivateEventPage"
import { handleLoaderErrors } from "../handleLoaderErrors"
import { PRIVATE_EVENT_ROUTE } from "../paths"

import { indexRootRoute } from "./index.root.route"

export const privateEventRute = createRoute({
  getParentRoute: () => indexRootRoute,
  staleTime: CHANGING_LISTS_STALE_TIME,
  path: PRIVATE_EVENT_ROUTE,
  loader: async () => {
    try {
      // Fetch ecents
    } catch (error) {
      return handleLoaderErrors("Can't find events", error)
    }
  },
  pendingComponent: LoadingPage,
  pendingMs: 300,
  component: PrivateEventPage,
})
