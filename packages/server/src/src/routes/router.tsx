import { Router<PERSON>rovider, createRouter } from "@tanstack/react-router"

import { GeneralErrorPage } from "../pages/common/generalErrorPage/GeneralErrorPage"
import { useGameStore } from "../store/useGamesStore"
import { useUserStore } from "../store/useUserStore"
import { trpc } from "../trpc/trpc"
import { firebaseApp } from "../utils/firebase"

import { communityRootRoute } from "./community/community.root.route"
import {
  communityListRoute,
  communityOpenRoute,
} from "./community/community.route"
import { communityEditRoute } from "./community/communityEdit.route"
import { createEventRoute } from "./community/createEvent.route"
import { communityEventsRoute } from "./community/events.route"
import { gameRoute } from "./community/game.route"
import { gamesRoute } from "./community/games.route"
import { memberRoute } from "./community/member.route"
import { membersRoute } from "./community/members.route"
import { eventRootRoute } from "./event/event.root.route"
import { eventEditRoute } from "./event/eventEdit.route"
import { eventGameRoute } from "./event/eventGame.route"
import { eventGamesRoute } from "./event/eventGames.route"
import { eventInfoRoute } from "./event/eventInfo.route"
import { eventJoinRoute } from "./event/eventJoin.route"
import { eventOrganizersRoute } from "./event/eventOrganizers.route"
import { eventParticipantRoute } from "./event/eventParticipant.route"
import { eventParticipantsRoute } from "./event/eventParticipants.route"
import { eventWizzardRoute } from "./event/eventWizzard.route"
import { communityJoinRoute } from "./index/communityJoin.route"
import { createCommunityRoute } from "./index/createCommunity.route"
import { createPrivateEventRoute } from "./index/createPrivateEventRoute"
import { eventsRoute } from "./index/events.route"
import { indexRootRoute } from "./index/index.root.route"
import { loggedinRoute, loggedoutRoute } from "./index/login.route"
import { privateEventRute } from "./index/privateEvent.route"
import { publicCommunitiesRoute } from "./index/publicCommunities.route"
import { tagListRoute } from "./index/tagList.route"
import { usersProfileRoute } from "./index/usersProfile.route"
import { usersProfileGameRoute } from "./index/usersProfileGame.route"
import { rootRoute } from "./root"

const routeTree = rootRoute.addChildren([
  eventRootRoute.addChildren([
    eventInfoRoute,
    eventJoinRoute,
    eventParticipantRoute,
    eventParticipantsRoute,
    eventGameRoute,
    eventGamesRoute,
    eventEditRoute,
    eventOrganizersRoute,
    eventWizzardRoute,
  ]),
  indexRootRoute.addChildren([
    communityListRoute,
    loggedinRoute,
    loggedoutRoute,
    usersProfileRoute,
    publicCommunitiesRoute,
    createCommunityRoute,
    communityJoinRoute,
    usersProfileGameRoute,
    tagListRoute,
    eventsRoute,
    createPrivateEventRoute,
    privateEventRute,
  ]),
  communityRootRoute.addChildren([
    communityOpenRoute,
    memberRoute,
    membersRoute,
    gameRoute,
    gamesRoute,
    communityEditRoute,
    createEventRoute,
    communityEventsRoute,
  ]),
])

export const router = createRouter({
  routeTree,
  context: {
    gameStore: null,
    auth: null,
    trpc,
    userData: null,
  },
  defaultNotFoundComponent: GeneralErrorPage,
})

declare module "@tanstack/react-router" {
  interface Register {
    router: typeof router
  }
}

export const RouterProviderWithContext = () => {
  const auth = firebaseApp
  const userData = useUserStore()
  const gameStore = useGameStore()

  return (
    <RouterProvider router={router} context={{ auth, userData, gameStore }} />
  )
}
