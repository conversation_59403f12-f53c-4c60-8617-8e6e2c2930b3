// ================== PUBLIC ROUTES

export const INDEX_ROUTE = "/"

export const PUBLIC_COMMUNITIES_ROUTE = "/public"

export const LOGGEDIN_ROUTE = "/loggedin"

export const LOGGEDOUT_ROUTE = "/loggedout"

export const PROFILE_ROUTE = "/profile"

export const TAG_LIST_ROUTE = "/tags"

export const PROFILE_GAME_ROUTE = "/profile/game/$gameId"

export const COMMUNITY_OPEN_ROUTE = "/communities"

export const CREATE_COMMUNITY_ROUTE = "/create-community"

export const COMMUNITY_JOIN_ROUTE = `/join`

export const EVENTS_ROUTE = "/events"

export const CREATE_EVENT_ROUTE = "/newEvent"

export const PRIVATE_EVENT_ROUTE = "/events/$eventId"

// ================== EVENT ROUTES

export const EVENT_ROOT_ROUTE = "/event/$eventId"
export const PART_EVENT_ROUTE_INFO = "/info"

export const EVENT_ROUTE_INFO = `${EVENT_ROOT_ROUTE}${PART_EVENT_ROUTE_INFO}`

export const PART_EVENT_JOIN_ROUTE = "/join"

export const EVENT_JOIN_ROUTE = `${EVENT_ROOT_ROUTE}${PART_EVENT_JOIN_ROUTE}`

export const PART_EVENT_PARTICIPANT_ROUTE = "/participants/$participantId"

export const EVENT_PARTICIPANT_ROUTE = `${EVENT_ROOT_ROUTE}${PART_EVENT_PARTICIPANT_ROUTE}`
export const PART_EVENT_PARTICIPANTS_ROUTE = "/participants"

export const EVENT_PARTICIPANTS_ROUTE = `${EVENT_ROOT_ROUTE}${PART_EVENT_PARTICIPANTS_ROUTE}`

export const PART_EVENT_GAMES_ROUTE = "/games"

export const EVENT_GAMES_ROUTE = `${EVENT_ROOT_ROUTE}${PART_EVENT_GAMES_ROUTE}`

export const PART_EVENT_GAME_ROUTE = "/game/$gameId"

export const EVENT_GAME_ROUTE = `${EVENT_ROOT_ROUTE}${PART_EVENT_GAME_ROUTE}`

export const PART_EVENT_EDIT_ROUTE = "/edit"

export const EVENT_EDIT_ROUTE = `${EVENT_ROOT_ROUTE}${PART_EVENT_EDIT_ROUTE}`

export const PART_EVENT_PROFILE_ROUTE = "/profile"

export const EVENT_PROFILE_ROUTE = `${EVENT_ROOT_ROUTE}${PART_EVENT_PROFILE_ROUTE}`

export const PART_ORGANIZERS_GAME_ROUTE = "/organizers"

export const EVENT_ORGANIZERS_ROUTE = `${EVENT_ROOT_ROUTE}${PART_ORGANIZERS_GAME_ROUTE}`

export const PART_EVENT_WIZZARD_ROUTE = "/wizzard"

export const EVENT_WIZZARD_ROUTE = `${EVENT_ROOT_ROUTE}${PART_EVENT_WIZZARD_ROUTE}`

// ================== COMMUNITY ROUTES

export const COMMUNITIES_ROOT_ROUTE = "/community/$communityId"

export const PART_COMMUNITY_ROUTE = "/info"
export const COMMUNITY_ROUTE = `${COMMUNITIES_ROOT_ROUTE}${PART_COMMUNITY_ROUTE}`

export const PART_GAME_ROUTE = "/game/$gameId"
export const GAME_ROUTE = `${COMMUNITIES_ROOT_ROUTE}${PART_GAME_ROUTE}`

export const PART_GAMES_ROUTE = "/games"
export const GAMES_ROUTE = `${COMMUNITIES_ROOT_ROUTE}${PART_GAMES_ROUTE}`

export const PART_COMMUNITY_USER_ROUTE = "/user/$userId"
export const COMMUNITY_USER_ROUTE = `${COMMUNITIES_ROOT_ROUTE}${PART_COMMUNITY_USER_ROUTE}`

export const PART_COMMUNITY_USERS_ROUTE = "/users"
export const COMMUNITY_USERS_ROUTE = `${COMMUNITIES_ROOT_ROUTE}${PART_COMMUNITY_USERS_ROUTE}`

export const PART_COMMUNITY_PROFILE_ROUTE = "/profile"
export const COMMUNITY_PROFILE_ROUTE = `${COMMUNITIES_ROOT_ROUTE}${PART_COMMUNITY_PROFILE_ROUTE}`

export const PART_COMMUNITY_EDIT_ROUTE = "/edit"
export const COMMUNITY_EDIT_ROUTE = `${COMMUNITIES_ROOT_ROUTE}${PART_COMMUNITY_EDIT_ROUTE}`

export const PART_COMMUNITY_EVENT = "/newEvent"
export const COMMUNITY_EVENT_ROUTE = `${COMMUNITIES_ROOT_ROUTE}${PART_COMMUNITY_EVENT}`

export const PART_COMMUNITY_EVENTS = "/events"
export const COMMUNITY_EVENTS_ROUTE = `${COMMUNITIES_ROOT_ROUTE}${PART_COMMUNITY_EVENTS}`
