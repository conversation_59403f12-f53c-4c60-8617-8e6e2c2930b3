import { createRoute } from "@tanstack/react-router"
import { z } from "zod"

import { DEFAULT_ORDER, DEFAULT_ORDER_BY } from "../../config/game.conf"
import { EVENT_STALE_TIME } from "../../config/routes"
import { LoadingPage } from "../../pages/common/LoadingPage/LoadingPage"
import { EventGamesPage } from "../../pages/event/eventGamesPage/EventGamesPage"
import { filterGamesSearchSchema } from "../../schemas"
import { handleLoaderErrors } from "../handleLoaderErrors"
import { PART_EVENT_GAMES_ROUTE } from "../paths"

import { eventRootRoute } from "./event.root.route"

import type { GameStoreCollection } from "../../store/useGamesStore"

const gamesSchema = z.object({})

export const eventGamesRouteSearchSchema = gamesSchema.extend(
  filterGamesSearchSchema.shape,
)

export const eventGamesRoute = createRoute({
  validateSearch: (search) => eventGamesRouteSearchSchema.parse(search),
  getParentRoute: () => eventRootRoute,
  path: PART_EVENT_GAMES_ROUTE,
  staleTime: EVENT_STALE_TIME,
  loader: async ({ context: { trpc, gameStore }, params: { eventId } }) => {
    try {
      const event = gameStore && gameStore.getGames("event", parseInt(eventId))

      if (event) {
        return
      }

      const gamesReturned = await Promise.all([
        trpc.event.games.list.query({
          eventId: parseInt(eventId),
        }),
      ])

      const newGame: GameStoreCollection = {
        ...gamesReturned[0],
        meta: {
          order: DEFAULT_ORDER,
          orderBy: DEFAULT_ORDER_BY,
          id: parseInt(eventId),
          type: "event",
        },
      }

      gameStore?.setGames(newGame)
    } catch (error) {
      return handleLoaderErrors("Can't find games", error)
    }
  },
  pendingComponent: LoadingPage,
  pendingMs: 300,
  component: EventGamesPage,
})
