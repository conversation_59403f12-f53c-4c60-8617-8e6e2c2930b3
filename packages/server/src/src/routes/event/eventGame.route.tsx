import { createRoute } from "@tanstack/react-router"
import { z } from "zod"

import { EVENT_STALE_TIME } from "../../config/routes"
import { LoadingPage } from "../../pages/common/LoadingPage/LoadingPage"
import { EventGamePage } from "../../pages/event/eventGamePage/EventGamePage"
import { handleLoaderErrors } from "../handleLoaderErrors"
import { PART_EVENT_GAME_ROUTE } from "../paths"

import { eventRootRoute } from "./event.root.route"

const eventGameRouteSearchSchema = z.object({
  userId: z.number().optional(),
  hideOthers: z.boolean().optional(),
  search: z.string().optional(),
  sourcePage: z.string().optional(),
  sourceProps: z.string().optional(),
  sourceParams: z.string().optional(),
})

export const eventGameRoute = createRoute({
  validateSearch: (search) => eventGameRouteSearchSchema.parse(search),
  getParentRoute: () => eventRootRoute,
  path: PART_EVENT_GAME_ROUTE,
  staleTime: EVENT_STALE_TIME,
  loader: async ({ context: { trpc }, params: { eventId, gameId } }) => {
    try {
      return await trpc.event.games.info.query({
        eventId: parseInt(eventId),
        gameId: parseInt(gameId),
      })
    } catch (error) {
      return handleLoaderErrors("Can't find game", error)
    }
  },
  pendingComponent: LoadingPage,
  pendingMs: 300,
  component: EventGamePage,
})
