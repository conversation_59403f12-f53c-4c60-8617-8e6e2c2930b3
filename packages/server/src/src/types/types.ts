export type EventWizzardState =
  | "skip"
  | "waiting"
  | "settings"
  | "pick-games"
  | "review-games"
  | "done"

export type EventWizzardFilter =
  | "event-willbring"
  | "event-non-cancelled"
  | "event-cancelled"
  | "event-wishlist"
  | "event-canask"
  | "none"

export type CommunityGameUserTuple = [number, number]

export type EventGameUserTuple = [number, number, string]

export type GameUserTuple = CommunityGameUserTuple | EventGameUserTuple

export type GameFilterSwitch = "event" | "eventWizard" | "community"

export type CollectionType = "event" | "community"

export type EventGameStatus =
  | "wish"
  | "willbring"
  | "canask"
  | "willnotbring"
  | "willplay"
  | "maybeplay"

export type EventGameStatusReset = EventGameStatus | "reset"
