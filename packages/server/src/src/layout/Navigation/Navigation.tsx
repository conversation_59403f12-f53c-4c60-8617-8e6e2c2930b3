import MenuIcon from "@mui/icons-material/Menu"
import {
  App<PERSON><PERSON>,
  Box,
  Container,
  Drawer,
  IconButton,
  <PERSON><PERSON><PERSON>,
} from "@mui/material"
import { useCallback, useState } from "react"

import { SkipLink } from "../../components/elements/SkipLink/SkipLink"
import { useIsMobileStore } from "../../store/useIsMobileStore"

import { BaseInfo } from "./BaseInfo"
import { CommunityInfo } from "./CommunityInfo"
import { EventInfo } from "./EventInfo"
import { DrawerButtons as BasicDrawerButtons } from "./basic/DrawerButtons"
import { NavigationButtons as BasicNavigationButtons } from "./basic/NavigationButtons"
import { DrawerButtons as CommunityDrawerButtons } from "./community/DrawerButtons"
import { NavigationButtons as CommunityNavigationButtons } from "./community/NavigationButtons"
import { DrawerButtons as EventDrawerButtons } from "./event/DrawerButtons"
import { NavigationButtons as EventNavigationButtons } from "./event/NavigationButtons"

interface NavigationProps {
  pad?: boolean
  type?: "basic" | "community" | "event"
}

export const Navigation = ({ pad = false, type }: NavigationProps) => {
  const sizeThresholdList = useIsMobileStore((state) => state.sizeThresholdList)
  const [open, setOpen] = useState(false)

  const handleToggleDrawer = useCallback(
    (newOpen: boolean) => () => {
      setOpen(newOpen)
    },
    [setOpen],
  )

  let DrawerButtons
  let NavigationButtons

  switch (type) {
    case "community":
      NavigationButtons = CommunityNavigationButtons
      DrawerButtons = CommunityDrawerButtons
      break
    case "event":
      NavigationButtons = EventNavigationButtons
      DrawerButtons = EventDrawerButtons
      break
    default:
      NavigationButtons = BasicNavigationButtons
      DrawerButtons = BasicDrawerButtons
      break
  }

  return (
    <Box sx={{ flexGrow: 1, minHeight: "64px" }}>
      <AppBar position={sizeThresholdList.smallDesktop ? "fixed" : "static"}>
        <Container maxWidth="xl">
          <Toolbar disableGutters>
            <SkipLink />
            {!sizeThresholdList.smallDesktop && <NavigationButtons pad={pad} />}
            {sizeThresholdList.smallDesktop && (
              <Box
                display="flex"
                flexDirection="row"
                gap={2}
                justifyContent="space-between"
                width="100%"
              >
                <IconButton
                  size="large"
                  edge="start"
                  color="inherit"
                  aria-label="menu"
                  onClick={handleToggleDrawer(!open)}
                  sx={{ mr: 2 }}
                >
                  <MenuIcon />
                </IconButton>
                {type === "community" && <CommunityInfo />}
                {type === "basic" && <BaseInfo />}
                {type === "event" && <EventInfo />}
                <Drawer open={open} onClose={handleToggleDrawer(false)}>
                  {<DrawerButtons toggleDrawer={handleToggleDrawer(false)} />}
                </Drawer>
              </Box>
            )}
          </Toolbar>
        </Container>
      </AppBar>
    </Box>
  )
}
