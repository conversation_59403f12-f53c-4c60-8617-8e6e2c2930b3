import { Box, Typography } from "@mui/material"
import classnames from "classnames"

import { PartyBox } from "../../components/elements/link/PartyBox/PartyBox"
import { createImageLink } from "../../config/images"
import { eventRootRoute } from "../../routes/event/event.root.route"
import { EVENT_ROUTE_INFO } from "../../routes/paths"

import * as styles from "./navigation.module.css"

export const EventInfo = () => {
  const eventData = eventRootRoute.useLoaderData()

  const id = String(eventData?.id ?? 0)

  return (
    <Box display="flex" flexDirection="row" gap={2} alignItems="center">
      <Typography variant="h4" className={styles.name}>
        {eventData?.title ?? ""}
      </Typography>
      <PartyBox
        to={EVENT_ROUTE_INFO}
        params={{ eventId: id }}
        title={eventData?.title ?? ""}
        className={classnames(styles.smallLogo)}
        sx={{
          backgroundImage: `url(${createImageLink(
            "event",
            "small",
            eventData?.id ?? 0,
            eventData?.image,
          )})`,
        }}
      />
    </Box>
  )
}
