.logo {
  cursor: pointer;
  border-radius: 10px;
  overflow: hidden;
  width: 100px;
  height: 100px;
  background-size: cover;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.nonMobile {
  top: 10px;
  left: 0;
  position: absolute;
  right: 16px;
  z-index: 2000;
  box-shadow:
    0 2px 4px -1px rgba(0, 0, 0, 0.2),
    0 4px 5px 0 rgba(110, 168, 192, 0.9),
    0 1px 10px 0 rgba(0, 0, 0, 0.12);
}

.smallLogo {
  cursor: pointer;
  border-radius: 10px;
  overflow: hidden;
  width: 50px;
  height: 50px;
  background-size: cover;
}

.list {
  align-items: center;
  display: flex;
  flex-direction: column;
}

.listItem {
  text-align: center;
}

.listItemMain span {
  text-align: center;
  font-size: 1.5rem;
}

.name {
  text-overflow: ellipsis;
  text-wrap-mode: nowrap;
  overflow: hidden;
}

.navigateName {
    text-overflow: ellipsis;
    overflow: hidden;
    font-size: 1.5rem;
    margin-left: 100px;
    max-width: 200px;
    line-clamp: 1;
    -webkit-line-clamp: 1;
    display: -webkit-box;
    -webkit-box-orient: vertical;
}