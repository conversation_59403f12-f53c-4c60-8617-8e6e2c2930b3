import { Box, Divider, List } from "@mui/material"
import classnames from "classnames"

import { LoginButton } from "../../../components/common/LoginButton/LoginButton"
import { PartyBox } from "../../../components/elements/link/PartyBox/PartyBox"
import { PartyLink } from "../../../components/elements/link/PartyLink/PartyLink"
import { createImageLink } from "../../../config/images"
import { hasPermission } from "../../../permissions"
import { eventRootRoute } from "../../../routes/event/event.root.route"
import {
  COMMUNITY_ROUTE,
  EVENT_EDIT_ROUTE,
  EVENT_GAMES_ROUTE,
  EVENT_ORGANIZERS_ROUTE,
  EVENT_PARTICIPANTS_ROUTE,
  EVENT_PARTICIPANT_ROUTE,
  EVENT_ROUTE_INFO,
  INDEX_ROUTE,
} from "../../../routes/paths"
import { useUserStore } from "../../../store/useUserStore"
import { NavigationItem } from "../components/NavigationItem"
import * as styles from "../navigation.module.css"

interface DrawerButtonsProps {
  toggleDrawer: () => void
}
export const DrawerButtons = ({ toggleDrawer }: DrawerButtonsProps) => {
  const isLoggedIn = useUserStore((state) => state.isLoggedIn)

  const eventData = eventRootRoute.useLoaderData()
  const userInfo = useUserStore((state) => state.userData)

  const id = String(eventData?.id ?? 0)

  const isParticipant = hasPermission(
    userInfo,
    "event",
    "isParticipant",
    eventData,
  )

  const onClose = () => {
    toggleDrawer()
  }

  const organizer = eventData?.communities.find((community) => community.owner)

  return (
    <Box
      onClick={onClose}
      display="flex"
      flexDirection="column"
      justifyContent="space-between"
      alignItems="center"
      gap={2}
      height="100%"
      pb={8}
    >
      {isLoggedIn && (
        <>
          <List className={styles.list}>
            <PartyBox
              to={EVENT_ROUTE_INFO}
              params={{ eventId: id }}
              title={eventData?.title ?? ""}
              className={classnames(styles.logo)}
              sx={{
                backgroundImage: `url(${createImageLink(
                  "event",
                  "small",
                  eventData?.id ?? 0,
                  eventData?.image,
                )})`,
              }}
            />
            <NavigationItem
              text={eventData?.title ?? ""}
              route={EVENT_ROUTE_INFO}
              params={{ eventId: id }}
              isMain
            />
            <NavigationItem
              text="Games"
              route={EVENT_GAMES_ROUTE}
              params={{ eventId: id }}
            />
            {isParticipant && (
              <NavigationItem
                text="Participants"
                route={EVENT_PARTICIPANTS_ROUTE}
                params={{ eventId: id }}
              />
            )}
            <NavigationItem
              text="Organizers"
              route={EVENT_ORGANIZERS_ROUTE}
              params={{ eventId: id }}
            />
            {hasPermission(userInfo, "event", "update", {
              id: Number(id),
            }) && (
              <NavigationItem
                text="Administrate"
                route={EVENT_EDIT_ROUTE}
                params={{ eventId: id }}
              />
            )}
            {isParticipant && (
              <NavigationItem
                text="Profile"
                route={EVENT_PARTICIPANT_ROUTE}
                params={{
                  eventId: id,
                  participantId: String(userInfo?.id ?? 0),
                }}
              />
            )}
          </List>
          <Divider sx={{ width: "100%" }} />
        </>
      )}
      <Box
        display="flex"
        gap={0.5}
        alignItems="center"
        flexDirection="column"
        justifyContent="center"
      >
        <Box
          display="flex"
          gap={0.5}
          alignItems="center"
          flexDirection="column"
          justifyContent="center"
        >
          {organizer && (
            <PartyBox
              to={COMMUNITY_ROUTE}
              params={{ communityId: String(organizer.id) }}
              title={organizer.name}
              className={classnames(styles.smallLogo)}
              sx={{
                marginTop: "4px",
                backgroundImage: `url(${createImageLink(
                  "community",
                  "small",
                  organizer?.id ?? 0,
                  organizer?.image,
                )})`,
              }}
            />
          )}
          {organizer && (
            <PartyLink
              to={COMMUNITY_ROUTE}
              params={{ communityId: String(organizer?.id) }}
              color="inherit"
              variant="text"
            >
              {organizer.name}
            </PartyLink>
          )}
          <PartyLink to={INDEX_ROUTE} color="inherit" variant="text">
            Home
          </PartyLink>
        </Box>
        <LoginButton />
      </Box>
    </Box>
  )
}
