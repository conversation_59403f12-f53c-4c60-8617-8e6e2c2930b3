import { Box } from "@mui/material"
import classnames from "classnames"

import { LoginButton } from "../../../components/common/LoginButton/LoginButton"
import { PartyBox } from "../../../components/elements/link/PartyBox/PartyBox"
import { PartyLink } from "../../../components/elements/link/PartyLink/PartyLink"
import { createImageLink } from "../../../config/images"
import { hasPermission } from "../../../permissions"
import { eventRootRoute } from "../../../routes/event/event.root.route"
import {
  COMMUNITY_ROUTE,
  EVENT_EDIT_ROUTE,
  EVENT_GAMES_ROUTE,
  EVENT_ORGANIZERS_ROUTE,
  EVENT_PARTICIPANTS_ROUTE,
  EVENT_PARTICIPANT_ROUTE,
  EVENT_ROUTE_INFO,
  INDEX_ROUTE,
} from "../../../routes/paths"
import { useUserStore } from "../../../store/useUserStore"
import * as styles from "../navigation.module.css"
export const NavigationButtons = () => {
  const isLoggedIn = useUserStore((state) => state.isLoggedIn)
  const userInfo = useUserStore((state) => state.userData)
  const eventData = eventRootRoute.useLoaderData()

  const id = String(eventData?.id ?? 0)

  const organizer = eventData?.communities.find((community) => community.owner)

  console.info("organizer", organizer, eventData?.communities)

  const isParticipant = hasPermission(
    userInfo,
    "event",
    "isParticipant",
    eventData,
  )

  return (
    <>
      <Box justifyContent="space-between" display="flex" gap={2} width="100%">
        <Box display="flex" gap={2}>
          {isLoggedIn && (
            <>
              <PartyBox
                to={EVENT_ROUTE_INFO}
                params={{ eventId: id }}
                title={eventData?.title ?? ""}
                className={classnames(styles.logo, styles.nonMobile)}
                sx={{
                  backgroundImage: `url(${createImageLink(
                    "event",
                    "small",
                    eventData?.id ?? 0,
                    eventData?.image,
                  )})`,
                }}
              />
              <PartyLink
                color="inherit"
                size="large"
                className={styles.navigateName}
                variant="text"
                to={EVENT_ROUTE_INFO}
                params={{ eventId: id }}
              >
                {eventData?.title ?? ""}
              </PartyLink>
              <PartyLink
                color="inherit"
                variant="text"
                to={EVENT_GAMES_ROUTE}
                params={{ eventId: id }}
                preload="intent"
                preloadDelay={500}
              >
                Games
              </PartyLink>
              {isParticipant && (
                <PartyLink
                  color="inherit"
                  variant="text"
                  to={EVENT_PARTICIPANTS_ROUTE}
                  params={{ eventId: id }}
                  preload="intent"
                  preloadDelay={500}
                >
                  Participants
                </PartyLink>
              )}
              <PartyLink
                color="inherit"
                variant="text"
                to={EVENT_ORGANIZERS_ROUTE}
                params={{ eventId: id }}
                preload="intent"
                preloadDelay={500}
              >
                Organizers
              </PartyLink>
              {hasPermission(userInfo, "event", "update", {
                id: Number(id),
              }) && (
                <PartyLink
                  color="inherit"
                  variant="text"
                  to={EVENT_EDIT_ROUTE}
                  params={{ eventId: id }}
                  preload="intent"
                  preloadDelay={500}
                >
                  Administrate
                </PartyLink>
              )}
              {isParticipant && (
                <PartyLink
                  to={EVENT_PARTICIPANT_ROUTE}
                  params={{
                    participantId: String(userInfo?.id ?? 0),
                    eventId: id,
                  }}
                  color="inherit"
                  variant="text"
                >
                  Profile
                </PartyLink>
              )}
            </>
          )}
        </Box>

        <Box display="flex" gap={0.5}>
          {organizer && (
            <PartyBox
              to={COMMUNITY_ROUTE}
              params={{ communityId: String(organizer?.id) }}
              title={organizer?.name}
              className={classnames(styles.smallLogo)}
              sx={{
                marginTop: "4px",
                backgroundImage: `url(${createImageLink(
                  "community",
                  "small",
                  organizer?.id ?? 0,
                  organizer?.image,
                )})`,
              }}
            />
          )}
          {organizer && (
            <PartyLink
              to={COMMUNITY_ROUTE}
              params={{ communityId: String(organizer?.id) }}
              color="inherit"
              variant="text"
            >
              {organizer.name}
            </PartyLink>
          )}
          <PartyLink to={INDEX_ROUTE} color="inherit" variant="text">
            Home
          </PartyLink>
          <LoginButton />
        </Box>
      </Box>
    </>
  )
}
