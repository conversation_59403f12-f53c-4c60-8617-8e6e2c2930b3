import { useCallback } from "react"

import { router } from "../routes/router"
import { useToastStore } from "../store/useToastStore"
import { trpc } from "../trpc/trpc"
import { IEventBaseData } from "../types/tRPC.types"

import { useUserDataLoader } from "./useUserDataLoader"

export const useEvent = () => {
  const { reFetch } = useUserDataLoader()
  const { setTrpcError } = useToastStore()

  const onChangeUserState = useCallback(
    async ({
      event,
      status,
      onComplete,
    }: {
      event: Pick<IEventBaseData, "id">
      onComplete: (role: string | null) => void
      status:
        | "participant"
        | "interested"
        | "requested"
        | "reserved"
        | "notgoing"
    }) => {
      try {
        const result = await trpc.protected.user.do.updateEventStatus.mutate({
          eventId: event.id,
          status,
        })

        onComplete(result.role)
        await router.invalidate()
        await reFetch()
      } catch (error: unknown) {
        setTrpcError(error)
      }
    },
    [reFetch],
  )

  return { onChangeUserState }
}
