import { Button, ButtonGroup, Icon } from "@mui/material"

import { userGamaStateStrings } from "../../../config/userGameStateStrings"
import { EventGameStatusReset } from "../../../types/types"

type StatusButtonsProps = {
  onClick: (status: EventGameStatusReset) => void
  status: EventGameStatusReset | null
  own: boolean
  small?: boolean
}
export const GameStatusButtons = ({
  onClick,
  status,
  own,
  small = false,
}: StatusButtonsProps) => {
  const handleClick = (
    event: React.MouseEvent<HTMLButtonElement>,
    status: EventGameStatusReset,
  ) => {
    event.stopPropagation()
    event.preventDefault()
    onClick?.(status)
  }

  return (
    <ButtonGroup>
      {own && status !== "willbring" && (
        <Button
          size={small ? "small" : "medium"}
          key="willbring"
          onClick={(e) => handleClick(e, "willbring")}
          title={userGamaStateStrings["willbring"].titleMy}
          color={userGamaStateStrings["willbring"].color}
        >
          <Icon>{userGamaStateStrings["willbring"].icon}</Icon>
        </Button>
      )}
      {own && status !== "canask" && (
        <Button
          size={small ? "small" : "medium"}
          key="canask"
          onClick={(e) => handleClick(e, "canask")}
          title={userGamaStateStrings["canask"].titleMy}
          color={userGamaStateStrings["canask"].color}
        >
          <Icon>{userGamaStateStrings["canask"].icon}</Icon>
        </Button>
      )}
      {status !== "willplay" && status !== "wish" && status !== "willbring" && (
        <Button
          size={small ? "small" : "medium"}
          key="willplay"
          onClick={(e) => handleClick(e, "willplay")}
          title={userGamaStateStrings["willplay"].titleMy}
          color={userGamaStateStrings["willplay"].color}
        >
          <Icon>{userGamaStateStrings["willplay"].icon}</Icon>
        </Button>
      )}
      {status !== "maybeplay" && status !== "willbring" && (
        <Button
          size={small ? "small" : "medium"}
          key="maybeplay"
          onClick={(e) => handleClick(e, "maybeplay")}
          title={userGamaStateStrings["maybeplay"].titleMy}
          color={userGamaStateStrings["maybeplay"].color}
        >
          <Icon>{userGamaStateStrings["maybeplay"].icon}</Icon>
        </Button>
      )}
      {own && status !== "willnotbring" && (
        <Button
          size={small ? "small" : "medium"}
          key="willnotbring"
          onClick={(e) => handleClick(e, "willnotbring")}
          color={userGamaStateStrings["willnotbring"].color}
          title={userGamaStateStrings["willnotbring"].titleMy}
        >
          <Icon>{userGamaStateStrings["willnotbring"].icon}</Icon>
        </Button>
      )}
      {!own && status !== "reset" && status !== null && (
        <Button
          size={small ? "small" : "medium"}
          key="reset"
          onClick={(e) => handleClick(e, "reset")}
          color={userGamaStateStrings["reset"].color}
          title={userGamaStateStrings["reset"].titleMy}
        >
          <Icon>{userGamaStateStrings["reset"].icon}</Icon>
        </Button>
      )}
    </ButtonGroup>
  )
}
