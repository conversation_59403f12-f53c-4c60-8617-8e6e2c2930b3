import { Fade, Snackbar } from "@mui/material"
import { useState } from "react"

import { hasPermission } from "../../../../../../common/src/permissions/hasPermissions"
import { eventRootRoute } from "../../../routes/event/event.root.route"
import { EVENT_WIZZARD_ROUTE } from "../../../routes/paths"
import { useUserStore } from "../../../store/useUserStore"
import { isEvent } from "../../../utils/pages.rootObject"
import { PartyLink } from "../../elements/link/PartyLink/PartyLink"

export const FillEventData = () => {
  const event = eventRootRoute.useLoaderData()
  const user = useUserStore((state) => state.userData)

  const isDone =
    event &&
    isEvent(event) &&
    event.wizzardState !== "skip" &&
    event.wizzardState !== "done"

  const [open, setOpen] = useState(isDone)

  const isParticipant = hasPermission(user, "event", "isParticipant", event)

  if (
    !event ||
    !isEvent(event) ||
    !event.id ||
    event.userEvent === undefined ||
    event.wizzardState === undefined ||
    !isParticipant
  ) {
    return null
  }

  const handleClose = () => {
    setOpen(false)
  }

  const action = (
    <PartyLink
      color="primary"
      variant="outlined"
      size="small"
      to={EVENT_WIZZARD_ROUTE}
      params={{ eventId: String(event.id) }}
      onClick={handleClose}
    >
      Open Wizard
    </PartyLink>
  )

  return (
    <Snackbar
      anchorOrigin={{ vertical: "top", horizontal: "center" }}
      open={open}
      onClose={handleClose}
      TransitionComponent={Fade}
      action={action}
      autoHideDuration={5000}
      message="Please Your fill event information"
    />
  )
}
