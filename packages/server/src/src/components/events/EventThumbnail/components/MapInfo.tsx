import MapIcon from "@mui/icons-material/Map"
import { Box, Button, Typography } from "@mui/material"
import { useCallback } from "react"

import { useMapLocationStore } from "../../../../store/useMapLocationStore"
import { ICEvent } from "../../../../types/tRPC.types"
import { MAP_MODAL_NAME, useModalStore } from "../../../modals"
import * as styles from "../eventThumbnail.module.css"

export const MapInfo = ({ event }: { event: ICEvent }) => {
  const locationChange = useMapLocationStore((state) => state.locationChange)
  const { openModal } = useModalStore()

  const handleOpenMap = useCallback(() => {
    if (event.lat && event.lng) {
      locationChange(event.lat, event.lng, event.location ?? "")
      openModal(MAP_MODAL_NAME)
    }
  }, [event.lat, event.lng, event.location])

  if (!event.location) {
    return null
  }

  return (
    <Box
      display="flex"
      gap={1}
      alignItems="center"
      flexWrap="nowrap"
      title={event.location}
      aria-label={event.location}
    >
      <MapIcon />
      <Typography variant="body2" className={styles.location}>
        {event.location}
      </Typography>
      {event.lat && event.lng && (
        <Button
          onClick={handleOpenMap}
          variant="outlined"
          size="small"
          className={styles.mapButton}
        >
          See map
        </Button>
      )}
    </Box>
  )
}
