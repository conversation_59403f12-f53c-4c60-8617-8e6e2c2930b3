import { <PERSON><PERSON>, <PERSON>, <PERSON>ton, <PERSON>tonGroup, Typography } from "@mui/material"
import { useNavigate } from "@tanstack/react-router"
import { useCallback } from "react"

import { useEvent } from "../../../../hooks/useEvent"
import { hasPermission } from "../../../../permissions"
import { EVENT_JOIN_ROUTE, EVENT_ROUTE_INFO } from "../../../../routes/paths"
import { useUserStore } from "../../../../store/useUserStore"
import { ICEvent } from "../../../../types/tRPC.types"
import { LabelTooltip } from "../../../elements/LabelTooltip/LabelTooltip"
import { LoadingButtonWithError } from "../../../elements/LoadingButtonWithError/LoadingButtonWithError"
import { PartyLink } from "../../../elements/link/PartyLink/PartyLink"

type EventButtonsProps = {
  event: ICEvent
  source: "card" | "page"
}

export const EventButtons = ({ event, source }: EventButtonsProps) => {
  const { onChangeUserState } = useEvent()
  const navigate = useNavigate()

  const handleComplete = useCallback(
    () => (status: string | null) => {
      if (status === "going" && source === "card") {
        navigate({
          to: EVENT_JOIN_ROUTE,
          params: { eventId: String(event.id) },
        })
        return
      }

      if (
        (status === "requested" ||
          status === "interested" ||
          status === "reserved") &&
        source === "card"
      ) {
        navigate({
          to: EVENT_ROUTE_INFO,
          params: { eventId: String(event.id) },
        })
      }
    },
    [event.id, source, navigate],
  )

  const onChangeStatus = useCallback(
    async (
      status:
        | "participant"
        | "interested"
        | "requested"
        | "reserved"
        | "notgoing",
    ) => {
      await onChangeUserState({ event, status, onComplete: handleComplete })
    },
    [event.id],
  )

  const stillHasReserve =
    (event?.maxCapacity ?? 0) > 0 &&
    (event?.going ?? 0) >= (event?.maxCapacity ?? 0) &&
    (event?.reserveCapacity ?? 0) > 0 &&
    (event?.reserve ?? 0) < (event?.reserveCapacity ?? 0)

  const user = useUserStore((state) => state.userData)

  const isParticipant = hasPermission(user, "event", "isParticipant", event)
  const isWaiting = hasPermission(user, "event", "isWaiting", event)
  const isRequested = hasPermission(user, "event", "isRequested", event)
  const isReserved = hasPermission(user, "event", "isReserved", event)
  const isHost = hasPermission(user, "event", "isHost", event)
  const canJoin = hasPermission(user, "event", "join", event)

  if (!event || (isHost && source === "page")) {
    return null
  }

  return (
    <Box
      borderTop={source === "page" ? "1px solid #ccc" : "none"}
      pt={2}
      width="100%"
    >
      <ButtonGroup>
        {!isParticipant && canJoin && stillHasReserve && (
          <>
            <Alert>Only reserve spots left!</Alert>
            <Button size="small" onClick={() => onChangeStatus("reserved")}>
              Reserve
            </Button>
            <LabelTooltip>
              <Typography variant="body1">
                Reserve - You will be added to reserve list. If someone drops
                out you will be moved to participant list.
              </Typography>
            </LabelTooltip>
          </>
        )}
        {!isParticipant && canJoin && !isRequested && !isReserved && (
          <Button
            size="small"
            onClick={() =>
              onChangeStatus(event.memberApproval ? "requested" : "participant")
            }
          >
            {event.memberApproval ? "Request" : "Going"}
          </Button>
        )}
        {!isParticipant && !isWaiting && canJoin && (
          <Button size="small" onClick={() => onChangeStatus("interested")}>
            Interested
          </Button>
        )}
        {source === "card" && (
          <PartyLink
            variant="contained"
            to={EVENT_ROUTE_INFO}
            params={{ eventId: String(event.id) }}
          >
            Open Event
          </PartyLink>
        )}
        {(isParticipant || isWaiting) && !isHost && (
          <LoadingButtonWithError
            title="Not Going"
            size="small"
            displayConfirm
            confirmMessage="Do You really want to cancel Your participation?"
            successMessage="You have cancelled Your participation"
            onClick={async () => await onChangeStatus("notgoing")}
          />
        )}
      </ButtonGroup>
    </Box>
  )
}
