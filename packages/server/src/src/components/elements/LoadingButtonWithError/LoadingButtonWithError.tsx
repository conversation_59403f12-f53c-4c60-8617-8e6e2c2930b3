import { LoadingButton, type LoadingButtonProps } from "@mui/lab"
import { useCallback, useState } from "react"

import { useToastStore } from "../../../store/useToastStore"
import { CONFIRM_MODAL_NAME, useModalStore } from "../../modals"

interface LoadingButtonWithErrorProps<S> extends LoadingButtonProps {
  onClick: () => Promise<S>
  onSuccess?: (response: S) => void
  onErrorReact?: (response: string) => void
  title: string
  errorMessage?: string
  errorTitle?: string
  displayError?: boolean
  displayConfirm?: boolean
  confirmPreferredButton?: "confirm" | "cancel"
  confirmMessage?: string
  successMessage?: string
}

type ComponentProps<S> = React.PropsWithChildren<LoadingButtonWithErrorProps<S>>

export const LoadingButtonWithError = <S,>({
  onClick,
  onErrorReact,
  onSuccess,
  title,
  errorMessage,
  successMessage,
  displayConfirm,
  confirmMessage = "Are You sure?",
  confirmPreferredButton = "cancel",
  ...props
}: ComponentProps<S>) => {
  const [loading, setLoading] = useState(false)
  const { setError, setMessage } = useToastStore()
  const { openModal } = useModalStore()

  const playOnclick = useCallback(() => {
    onClick()
      .then((result: S) => {
        onSuccess?.(result)
        if (successMessage) {
          setMessage({
            message: successMessage,
            code: "OK",
            severity: "success",
          })
        }
      })
      .catch((error: { message?: string }) => {
        setError({
          message: error.message ?? errorMessage ?? "Unknown error",
          code: "UNKNOWN",
        })
        if (onErrorReact) {
          onErrorReact(error as string)
        }
      })
      .finally(() => {
        setLoading(false)
      })
  }, [onClick, onSuccess, onErrorReact, setLoading, errorMessage, setError])

  const handleClick = useCallback(() => {
    setLoading(true)
    if (displayConfirm) {
      openModal(CONFIRM_MODAL_NAME, {
        message: confirmMessage ?? "",
        onConfirm: playOnclick,
        severity: "warning",
        preferredButton: confirmPreferredButton,
        onCancel: () => {
          setLoading(false)
        },
        title,
      })
    } else {
      playOnclick()
    }
  }, [
    displayConfirm,
    confirmMessage,
    title,
    playOnclick,
    openModal,
    setLoading,
  ])

  return (
    <>
      <LoadingButton {...props} onClick={handleClick} loading={loading}>
        {title}
      </LoadingButton>
    </>
  )
}
