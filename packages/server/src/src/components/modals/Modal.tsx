import { <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, DialogTitle } from "@mui/material"
import classnames from "classnames"
import { PropsWithChildren, useEffect } from "react"

import { useModalStore } from "../../store/useModalStore"

import * as styles from "./modal.module.css"

type ModalProps = {
  name: string
  title: React.ReactNode
  allowOverflow?: boolean
  onCancel?: () => void
} & PropsWithChildren

export const Modal = ({
  children,
  name,
  title,
  allowOverflow = false,
  onCancel,
}: ModalProps) => {
  // Only select the isOpen value to prevent unnecessary rerenders
  const isOpen = useModalStore(
    (state) => state.modalList[name]?.isOpen ?? false,
  )

  // Get functions separately to avoid including them in the reactive selector
  const { createModal, closeModal } = useModalStore()

  useEffect(() => {
    createModal(name)
  }, [])

  return (
    <Dialog
      open={isOpen}
      onClose={() => {
        onCancel?.()
        closeModal(name)
      }}
      PaperProps={{
        className: classnames({
          [styles.overflow]: allowOverflow,
        }),
      }}
    >
      <DialogTitle>{title}</DialogTitle>
      <DialogContent
        className={classnames({
          [styles.overflow]: allowOverflow,
        })}
      >
        <Box className={styles.close}>
          <Button
            variant="text"
            onClick={() => {
              onCancel?.()
              closeModal(name)
            }}
          >
            Close
          </Button>
        </Box>
        {children}
      </DialogContent>
    </Dialog>
  )
}
