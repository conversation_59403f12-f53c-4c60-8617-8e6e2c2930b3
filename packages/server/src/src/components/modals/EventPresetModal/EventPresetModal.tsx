import { zod<PERSON><PERSON>olver } from "@hookform/resolvers/zod"
import { LoadingButton } from "@mui/lab"
import { Box, Typography } from "@mui/material"
import { useState } from "react"
import { FormProvider, type SubmitHandler, useForm } from "react-hook-form"
import { z } from "zod"

import { useModalStore } from "../../../store/useModalStore"
import { useToastStore } from "../../../store/useToastStore"
import { trpc } from "../../../trpc/trpc"
import { LoaderDialogState } from "../../common/LoaderDialog/LoaderDialog"
import { FormInput } from "../../elements/HookElements/FormInput"
import { Modal } from "../Modal"

export const EVENT_PRESET_MODAL_NAME = "eventPreset"

export const createEventPresetInputs = z.object({
  title: z.string().min(3),
})

type EventPresetModalOptions = {
  eventId: number
}

export type EventPresetInputs = z.infer<typeof createEventPresetInputs>

export const EventPresetModal = () => {
  const modalOptions = useModalStore(
    (state) => state.modalOptions[EVENT_PRESET_MODAL_NAME],
  ) as EventPresetModalOptions
  const { closeModal } = useModalStore()

  const [savingData, setSavingData] = useState<LoaderDialogState>(null)
  const { setTrpcError } = useToastStore()
  const methods = useForm<EventPresetInputs>({
    resolver: zodResolver(createEventPresetInputs),
    defaultValues: {
      title: "",
    },
  })

  const onSubmit: SubmitHandler<EventPresetInputs> = async (data) => {
    setSavingData("loading")

    trpc.event.wizzard.do.createPreset
      .mutate({
        title: data.title,
        eventId: modalOptions.eventId,
      })
      .then(() => {
        closeModal(EVENT_PRESET_MODAL_NAME)
        setSavingData(null)
      })
      .catch((error: unknown) => {
        closeModal(EVENT_PRESET_MODAL_NAME)
        setSavingData(null)
        setTrpcError(error)
      })
  }

  return (
    <Modal name={EVENT_PRESET_MODAL_NAME} title="Event Preset">
      <FormProvider {...methods}>
        <form onSubmit={methods.handleSubmit(onSubmit)}>
          <Box display="flex" flexDirection="column" gap={2}>
            <Typography variant="body1">
              This will save selected games as a preset. ("Wishlist" and "Want
              to play" games will not be saved.)
            </Typography>
            <FormInput
              label="Title"
              name="title"
              required={true}
              placeholder="Preset title"
            />
            <LoadingButton
              variant="contained"
              color="primary"
              type="submit"
              loading={savingData === "loading"}
            >
              Save
            </LoadingButton>
          </Box>
        </form>
      </FormProvider>
    </Modal>
  )
}
