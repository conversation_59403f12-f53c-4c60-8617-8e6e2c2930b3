import { useModalStore } from "../../../store/useModalStore"
import { GameFilterSwitch } from "../../../types/types"
import {
  GameSearch,
  type GameSearchProps,
} from "../../games/GameSearch/GameSearch"
import { Modal } from "../Modal"

type SearchModalOptions = {
  enableFilters?: GameFilterSwitch
}

export const SEARCH_MODAL_NAME = "search"
export const SearchModal = ({ ...props }: GameSearchProps) => {
  const modalOptions = useModalStore(
    (state) => state.modalOptions[SEARCH_MODAL_NAME],
  ) as SearchModalOptions

  return (
    <Modal name={SEARCH_MODAL_NAME} title="Search & Filter" allowOverflow>
      <GameSearch
        {...props}
        enableFilters={modalOptions?.enableFilters}
        modal
      />
    </Modal>
  )
}
