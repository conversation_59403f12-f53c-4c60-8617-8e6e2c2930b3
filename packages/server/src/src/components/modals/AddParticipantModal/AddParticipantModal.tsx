import { Autocomplete, Box, Button, TextField, Typo<PERSON> } from "@mui/material"
import { useRouter } from "@tanstack/react-router"
import debounce from "debounce"
import { useCallback, useEffect, useMemo, useState } from "react"

import { SEARCH_STR_LENGTH } from "../../../config/game.conf"
import { useModalStore } from "../../../store/useModalStore"
import { useToastStore } from "../../../store/useToastStore"
import { trpc } from "../../../trpc/trpc"
import { UserAvatar } from "../../user/UserAvatar/UserAvatar"
import { Modal } from "../Modal"

export const ADD_PARTICIPANT_MODAL_NAME = "addParticipant"

type AddParticipantModalProps = {
  eventId: number
}

type User = {
  id: number
  name: string
  avatar: string | null
  bggUsername: string | null
  color: string | null
}

export const AddParticipantModal = ({ eventId }: AddParticipantModalProps) => {
  const [search, setSearch] = useState("")
  const [selected, setSelected] = useState<User | null>(null)
  const { setTrpcError } = useToastStore()
  const [users, setUsers] = useState<User[]>([])
  const router = useRouter()
  const { closeModal } = useModalStore()

  const onSearch = (search: string) => {
    setSearch(search)
  }

  const onSearchDelayed = useMemo(() => debounce(onSearch, 300), [onSearch])

  const handleInviteCommunity = useCallback(async () => {
    if (!selected) {
      return
    }
    try {
      await trpc.event.participant.do.updateStatus.mutate({
        eventId,
        userId: selected.id,
        status: "participant",
      })
      router.invalidate()
    } catch (error: unknown) {
      setTrpcError(error)
    } finally {
      closeModal(ADD_PARTICIPANT_MODAL_NAME)
    }
  }, [selected, eventId])

  const handleSelectOption = useCallback(
    (option: User | null) => {
      if (!option) {
        return
      }
      setSelected(option)
    },
    [setSelected],
  )

  useEffect(() => {
    if (search.length < SEARCH_STR_LENGTH) {
      return
    }
    trpc.protected.search.users.query({ search }).then((data) => {
      setUsers(data)
    })
  }, [search])

  return (
    <Modal name={ADD_PARTICIPANT_MODAL_NAME} title="Add Participant">
      <Box m={1} display="flex" flexDirection="column" gap={2} minWidth="300px">
        <Autocomplete
          options={users}
          getOptionKey={(value) => value.id.toString()}
          getOptionLabel={(value) => value.name}
          renderOption={(props, option) => {
            const { key, ...rest } = props
            return (
              <Box
                key={key}
                component="li"
                {...rest}
                display="flex"
                gap={1}
                alignItems="center"
              >
                <UserAvatar user={option} labelInfo="Participant: " />
                <Typography variant="body1" fontWeight={600}>
                  {option.name}
                </Typography>
                {option.bggUsername && (
                  <Typography variant="body2">
                    /{option.bggUsername}/
                  </Typography>
                )}
              </Box>
            )
          }}
          renderValue={(option) => (
            <Typography variant="body1" fontWeight={600}>
              {option.name}
            </Typography>
          )}
          renderInput={(params) => <TextField {...params} label="Search" />}
          onInputChange={(_, value) => onSearchDelayed(value)}
          onChange={(_, value) => handleSelectOption(value)}
        />

        {selected && (
          <Box>
            <Box
              display="flex"
              flexDirection="row"
              gap={2}
              alignItems="center"
              m={2}
            >
              <Box
                borderRadius="8px"
                overflow="hidden"
                maxWidth="100px"
                maxHeight="100px"
              >
                <UserAvatar
                  user={selected}
                  labelInfo="Participant: "
                  size="large"
                />
              </Box>
              <Box>
                <Typography variant="h6">{selected.name}</Typography>
                <Typography
                  variant="body2"
                  color="textSecondary"
                  textTransform="uppercase"
                >
                  {selected.bggUsername}
                </Typography>
              </Box>
            </Box>
            <Box m={2}>
              <Button
                variant="contained"
                color="primary"
                onClick={handleInviteCommunity}
              >
                Add as Participant
              </Button>
            </Box>
          </Box>
        )}
      </Box>
    </Modal>
  )
}
