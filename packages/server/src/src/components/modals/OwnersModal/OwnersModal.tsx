import { useUserStore } from "../../../store/useUserStore"
import { GameUserSort, ownerSort } from "../../../utils/ownerSort"
import { OwnersList } from "../../user/OwnersList/OwnersList"
import { Modal } from "../Modal"

interface OwnersListProps {
  userList: GameUserSort[]
  onViewUser: (user?: number) => void
  onOpenUserProfile: (user: number) => void
  filterUserId?: number
}

export const OWNERS_MODAL_NAME = "owners"
export const OwnersModal = ({
  onViewUser,
  onOpenUserProfile,
  userList,
  filterUserId,
}: OwnersListProps) => {
  const myUserId = useUserStore((state) => state.userData.id)
  const users = userList.sort(ownerSort(myUserId ?? 0))

  return (
    <Modal name={OWNERS_MODAL_NAME} title="Owners">
      <OwnersList
        userList={users}
        onViewUser={onViewUser}
        onOpenUserProfile={onOpenUserProfile}
        filterUserId={filterUserId}
      />
    </Modal>
  )
}
