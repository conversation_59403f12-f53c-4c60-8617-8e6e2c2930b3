import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, Button } from "@mui/material"

import { useModalStore } from "../../../store/useModalStore"
import { Modal } from "../Modal"

export const CONFIRM_MODAL_NAME = "confirm"

type ConfirmModalOptions = {
  message: string
  title?: string
  icon?: React.ReactNode
  onConfirm: (data: unknown) => void
  onCancel: (data: unknown) => void
  severity?: "success" | "info" | "warning" | "error"
  data: unknown
  preferredButton?: "confirm" | "cancel"
}

const defaultOptions: ConfirmModalOptions = {
  message: "",
  onConfirm: () => {},
  onCancel: () => {},
  data: undefined,
  title: "",
  severity: "info",
  icon: undefined,
  preferredButton: "confirm",
}

export const ConfirmModal = () => {
  const modalOptions = useModalStore(
    (state) => state.modalOptions[CONFIRM_MODAL_NAME],
  ) as ConfirmModalOptions

  const { closeModal } = useModalStore()

  const {
    icon,
    severity,
    message,
    onConfirm,
    onCancel,
    data,
    title,
    preferredButton,
  } = modalOptions ?? defaultOptions

  const useIcon: React.ReactNode | undefined = icon ?? undefined

  const handleConfirm = () => {
    closeModal(CONFIRM_MODAL_NAME)
    onConfirm(data)
  }

  const handleCancel = () => {
    closeModal(CONFIRM_MODAL_NAME)
    onCancel(data)
  }

  return (
    <Modal name={CONFIRM_MODAL_NAME} title="" onCancel={handleCancel}>
      <Box display="flex" flexDirection="column" gap={2} pt={2}>
        <Alert icon={useIcon} severity={severity ?? "info"}>
          {title && <AlertTitle>{title}</AlertTitle>}
          {message}
        </Alert>
        <Box
          display="flex"
          flexDirection="row"
          gap={2}
          justifyContent="flex-end"
        >
          <Button
            variant={preferredButton === "confirm" ? "contained" : "outlined"}
            onClick={handleConfirm}
          >
            Confirm
          </Button>
          <Button
            variant={preferredButton === "cancel" ? "contained" : "outlined"}
            onClick={handleCancel}
          >
            Cancel
          </Button>
        </Box>
      </Box>
    </Modal>
  )
}
