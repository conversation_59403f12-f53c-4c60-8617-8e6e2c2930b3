import BackpackIcon from "@mui/icons-material/Backpack"
import CloseIcon from "@mui/icons-material/Close"
import PlayCircleOutlineIcon from "@mui/icons-material/PlayCircleOutline"
import QuestionMarkIcon from "@mui/icons-material/QuestionMark"
import { Icon } from "@mui/material"

import { MaybePlayIcon } from "../../../../assets/svg"

type EventStatusIconProps = {
  status: string | null
}

export const EventStatusIcon = ({ status }: EventStatusIconProps) => {
  switch (status) {
    case "willbring":
      return (
        <Icon title="Will bring">
          <BackpackIcon color="info" />
        </Icon>
      )
    case "maybeplay":
      return (
        <Icon title="Maybe will play">
          <MaybePlayIcon />
        </Icon>
      )
    case "willplay":
    case "wish":
      return (
        <Icon title="Wants to play">
          <PlayCircleOutlineIcon color="success" />
        </Icon>
      )
    case "canask":
      return (
        <Icon title="If asked">
          <QuestionMarkIcon color="warning" />
        </Icon>
      )
    case "willnotbring":
      return (
        <Icon title="Will not bring">
          <CloseIcon color="error" />
        </Icon>
      )
    default:
      return null
  }
}
