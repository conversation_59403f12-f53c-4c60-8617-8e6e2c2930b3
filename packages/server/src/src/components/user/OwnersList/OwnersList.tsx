import { <PERSON>, Button } from "@mui/material"
import classnames from "classnames"
import React, { useCallback } from "react"

import { EventGameStatus } from "../../../../../../api/src/root/types/game.types"
import { useUserStore } from "../../../store/useUserStore"
import { OwnerInfo, UserInfo } from "../OwnerInfo/OwnerInfo"
import { UserAvatar } from "../UserAvatar/UserAvatar"

import { EventStatusIcon } from "./EventStatusIcon"
import * as styles from "./ownersList.module.css"

export type GameUser = {
  id: number
  name: string
  avatar: string | null
  color: string | null
  eventStatus?: EventGameStatus | null
} & UserInfo

interface OwnersListProps {
  userList: GameUser[]
  onViewUser: (user?: number) => void
  onOpenUserProfile: (user: number) => void
  showActive?: boolean
  filterUserId?: number
}

export const OwnersList = ({
  onViewUser,
  onOpenUserProfile,
  userList,
  showActive = true,
  filterUserId,
}: OwnersListProps) => {
  const myUserId = useUserStore((state) => state.userData.id)

  const handleUserProfileClick = useCallback(
    (event: React.MouseEvent<HTMLButtonElement>, id: number) => {
      event.stopPropagation()
      onOpenUserProfile(id)
    },
    [onOpenUserProfile],
  )

  return (
    <Box
      pb={0}
      gap={0.5}
      display="flex"
      flexDirection="column"
      flexWrap="wrap"
      className={styles.container}
    >
      {filterUserId && showActive && (
        <Box className={styles.userRow}>
          Looking at: {userList.find((user) => user.id === filterUserId)?.name}
        </Box>
      )}
      <Button
        title="Clear user filter"
        variant="text"
        onClick={() => onViewUser()}
      >
        Clear selection
      </Button>
      {userList.map((user) => (
        <Box key={user.id}>
          <Box
            className={classnames(styles.userRow, {
              [styles.selected]: user.id === filterUserId,
            })}
            onClick={() => onViewUser(user.id)}
          >
            <Box display="flex" alignItems="center" gap={2}>
              {user.eventStatus && (
                <EventStatusIcon status={user.eventStatus} />
              )}
              <UserAvatar user={user} labelInfo="Owner: " />
              {user.name}
            </Box>
            <Box display="flex" gap={0.5}>
              <Button
                title="Filter user owned items"
                variant="text"
                onClick={() => onViewUser(user.id)}
              >
                Owned
              </Button>
              {myUserId !== user.id && (
                <Button
                  title="Open users profile"
                  variant="outlined"
                  onClick={(event) => handleUserProfileClick(event, user.id)}
                >
                  Profile
                </Button>
              )}
            </Box>
          </Box>
          {user.id === filterUserId && <OwnerInfo user={user} />}
        </Box>
      ))}
    </Box>
  )
}
