import { Box } from "@mui/material"
import dayjs from "dayjs"
import localizedFormat from "dayjs/plugin/localizedFormat"
import { useMemo } from "react"

import { GameInfoRow } from "../../games/GameInfoRow/GameInfoRow"

import * as styles from "./ownerInfo.module.css"

export type UserInfo = {
  rating: number | null
  lastPlay: string | null
  playCount: number | null
  willTeach: boolean | null
  portability: string | null
  events: string | null
}

interface OwnerInfoProps {
  user: UserInfo
}
export const OwnerInfo = ({ user }: OwnerInfoProps) => {
  dayjs.extend(localizedFormat)

  const requiredProps: (keyof UserInfo)[] = [
    "rating",
    "lastPlay",
    "playCount",
    "willTeach",
    "portability",
    "events",
  ]

  const hasAtLeastOne = useMemo(() => {
    if (!user) return false
    return requiredProps.some(
      (prop) =>
        user[prop] !== null && user[prop] !== undefined && user[prop] !== 0,
    )
  }, [user, requiredProps])

  if (!hasAtLeastOne) {
    return <Box className={styles.container}>No info</Box>
  }

  return (
    <>
      <Box className={styles.container}>
        {user !== null && (
          <Box className={styles.cellGrid}>
            {(user.rating ?? 0) > 0 && (
              <GameInfoRow title="Rating:" value={String(user.rating)} />
            )}
            {user.lastPlay && (
              <GameInfoRow
                title="Last Play:"
                value={dayjs(user.lastPlay).format("ll")}
              />
            )}
            {(user.playCount ?? 0) > 0 && (
              <GameInfoRow
                title="Play count:"
                value={String(user.playCount ?? 0)}
              />
            )}
            {user.willTeach && <GameInfoRow title="Can teach:" value={"Yes"} />}
            {user.portability && (
              <GameInfoRow title="Portability:" value={user.portability} />
            )}
            {user.events && <GameInfoRow title="Events:" value={user.events} />}
          </Box>
        )}
      </Box>
    </>
  )
}
