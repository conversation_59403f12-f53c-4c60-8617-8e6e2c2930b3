import { <PERSON>, <PERSON>Item, <PERSON><PERSON><PERSON>Button, ListItemText } from "@mui/material"
import {
  ControlPosition,
  MapControl,
  useMapsLibrary,
} from "@vis.gl/react-google-maps"
import { FormEvent, useCallback, useState } from "react"

import { useAutocompleteSuggestions } from "./useAutocompleteSuggestions"

interface Props {
  onPlaceSelect: (place: google.maps.places.Place | null) => void
  controlPosition: ControlPosition
}
export const AutocompleteCustom = ({
  onPlaceSelect,
  controlPosition,
}: Props) => {
  const places = useMapsLibrary("places")

  const [inputValue, setInputValue] = useState<string>("")
  const { suggestions, resetSession } = useAutocompleteSuggestions(inputValue)

  const handleInput = useCallback((event: FormEvent<HTMLInputElement>) => {
    setInputValue((event.target as HTMLInputElement).value)
  }, [])

  const handleSuggestionClick = useCallback(
    async (suggestion: google.maps.places.AutocompleteSuggestion) => {
      if (!places) return
      if (!suggestion.placePrediction) return

      const place = suggestion.placePrediction.toPlace()

      await place.fetchFields({
        fields: [
          "viewport",
          "location",
          "formattedAddress",
          "svgIconMaskURI",
          "iconBackgroundColor",
        ],
      })

      setInputValue("")

      // calling fetchFields invalidates the session-token, so we now have to call
      // resetSession() so a new one gets created for further search
      resetSession()

      onPlaceSelect(place)
    },
    [places, onPlaceSelect],
  )

  return (
    <MapControl position={controlPosition}>
      <div className="autocomplete-control">
        <div className="autocomplete-container">
          <input
            value={inputValue}
            onInput={(event) => handleInput(event)}
            placeholder="Search for a place"
          />

          {suggestions.length > 0 && (
            <List
              sx={{
                width: "400px",
                backgroundColor: "background.paper",
                maxHeight: 200,
                overflow: "auto",
                "& ul": { padding: 0 },
              }}
            >
              {suggestions.map((suggestion, index) => {
                return (
                  <ListItem disablePadding key={index}>
                    <ListItemButton
                      onClick={() => handleSuggestionClick(suggestion)}
                      dense
                    >
                      <ListItemText
                        primary={suggestion.placePrediction?.text?.text}
                        secondary={
                          suggestion.placePrediction?.secondaryText?.text
                        }
                        primaryTypographyProps={{
                          fontSize: "0.775rem",
                        }}
                        secondaryTypographyProps={{
                          fontSize: "0.65rem",
                        }}
                      />
                    </ListItemButton>
                  </ListItem>
                )
              })}
            </List>
          )}
        </div>
      </div>
    </MapControl>
  )
}
