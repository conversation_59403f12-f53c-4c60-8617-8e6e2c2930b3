import { Button } from "@mui/material"

import { useFirebase } from "../../../hooks/useFirebase"
import { useToastStore } from "../../../store/useToastStore"
import { trpc } from "../../../trpc/trpc"
import { LOGIN_MODAL_NAME, useModalStore } from "../../modals"

export const LoginButton = () => {
  const openModal = useModalStore((state) => state.openModal)
  const { isAuthenticated, logout } = useFirebase()
  const { setTrpcError } = useToastStore()

  const handleLogin = async () => {
    openModal(LOGIN_MODAL_NAME)
  }

  const handleLogout = async () => {
    try {
      await trpc.protected.user.do.logout.query()
      await logout()
    } catch (error: unknown) {
      setTrpcError(error)
    }
  }

  if (isAuthenticated) {
    return (
      <Button variant="outlined" color="inherit" onClick={() => handleLogout()}>
        Logout
      </Button>
    )
  }

  return (
    <Button variant="outlined" color="inherit" onClick={() => handleLogin()}>
      Sign on/Log In
    </Button>
  )
}
