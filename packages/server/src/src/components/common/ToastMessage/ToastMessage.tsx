import { Alert, Snac<PERSON>bar } from "@mui/material"
import { useEffect } from "react"

import { useToastStore } from "../../../store/useToastStore"

export const ToastMessage = () => {
  const currentError = useToastStore((state) => state.currentError)
  const isDisplaying = useToastStore((state) => state.isDisplaying)
  const { markAsDisplayed, showNextError } = useToastStore()

  const handleClose = (reason?: string) => {
    if (reason === "clickaway") {
      return
    }
    if (currentError) {
      markAsDisplayed(currentError)
    }
  }

  // Show next error when current error is closed
  useEffect(() => {
    if (!isDisplaying && !currentError) {
      showNextError()
    }
  }, [isDisplaying, currentError, showNextError])

  useEffect(() => {
    if (!currentError) {
      return
    }
  }, [currentError])

  if (!currentError || !isDisplaying) {
    return null
  }

  return (
    <Snackbar
      anchorOrigin={{ vertical: "top", horizontal: "center" }}
      open={isDisplaying}
      autoHideDuration={3200}
      onClose={(_, reason) => handleClose(reason)}
    >
      <Alert
        onClose={() => handleClose("close")}
        severity={currentError.severity ?? "error"}
        variant="filled"
        sx={{ width: "100%" }}
      >
        {currentError.message}
      </Alert>
    </Snackbar>
  )
}
