import BackpackIcon from "@mui/icons-material/Backpack"
import CloseIcon from "@mui/icons-material/Close"
import PlayCircleOutlineIcon from "@mui/icons-material/PlayCircleOutline"
import QuestionMarkIcon from "@mui/icons-material/QuestionMark"
import {
  Box,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  type SelectChangeEvent,
  Typography,
} from "@mui/material"
import { useCallback } from "react"

import { EventWizzardFilter, GameFilterSwitch } from "../../../../types/types"

import * as styles from "./filter.module.css"

type FilterProps = {
  enableFilters?: GameFilterSwitch
  onChange: (filter: EventWizzardFilter | undefined) => void
  filter?: EventWizzardFilter | undefined
}

type FilterOption = {
  key: EventWizzardFilter
  title: string
  label: React.ReactNode
}

const defaultFilter: Record<GameFilterSwitch, EventWizzardFilter> = {
  event: "event-willbring",
  eventWizard: "none",
  community: "none",
}

export const Filter = ({ enableFilters, onChange, filter }: FilterProps) => {
  const filterOptions: FilterOption[] = []

  if (enableFilters) {
    if (enableFilters === "eventWizard" || enableFilters === "event") {
      filterOptions.push({
        key: "event-willbring",
        title:
          enableFilters === "event"
            ? "Show only games someone will bring to the event"
            : "Show only games I will bring",
        label: (
          <Box
            display="flex"
            flexDirection="row"
            gap={1}
            justifyContent="center"
            width="100%"
          >
            <BackpackIcon color="info" />
          </Box>
        ),
      })
    }
    if (enableFilters === "event") {
      filterOptions.push({
        key: "event-wishlist",
        title: "Show only games someone has in wishlist",
        label: (
          <Box
            display="flex"
            flexDirection="row"
            gap={1}
            justifyContent="center"
            width="100%"
          >
            <PlayCircleOutlineIcon color="info" />
          </Box>
        ),
      })
    }
    if (enableFilters === "event") {
      filterOptions.push({
        key: "event-canask",
        title: "Show only games that someone can ask for (not yet in bag)",
        label: (
          <Box
            display="flex"
            flexDirection="row"
            gap={1}
            justifyContent="center"
            width="100%"
          >
            <QuestionMarkIcon color="info" />
          </Box>
        ),
      })
    }
    if (enableFilters === "eventWizard") {
      filterOptions.push({
        key: "event-non-cancelled",
        label: (
          <Box
            display="flex"
            flexDirection="row"
            gap={1}
            alignItems="center"
            justifyContent="center"
            width="100%"
          >
            <BackpackIcon color="info" />
            <Typography>/</Typography>
            <QuestionMarkIcon color="inherit" />
          </Box>
        ),
        title: "Hide games I will not take",
      })
    }
    if (enableFilters === "eventWizard") {
      filterOptions.push({
        key: "event-cancelled",
        label: (
          <Box
            display="flex"
            flexDirection="row"
            gap={1}
            justifyContent="center"
            width="100%"
          >
            <CloseIcon color="error" />
          </Box>
        ),
        title: "Hide games I will not take",
      })
    }
  }

  const handleSelectFilter = useCallback(
    (e: SelectChangeEvent<unknown>) => {
      onChange(e.target.value as EventWizzardFilter)
    },
    [onChange],
  )

  if (filterOptions.length === 0) {
    return null
  }

  return (
    <Box className={styles.container}>
      <FormControl fullWidth>
        <InputLabel id={`filter-games-label-id`}>Filter</InputLabel>
        <Select
          label="Filter"
          labelId={`filter-games-label-id`}
          onChange={handleSelectFilter}
          value={filter ?? defaultFilter[enableFilters ?? "event"]}
        >
          <MenuItem value="none" key="none">
            <Box
              display="flex"
              flexDirection="row"
              gap={1}
              alignItems="center"
              justifyContent="center"
              width="100%"
            >
              None
            </Box>
          </MenuItem>
          {filterOptions.map((item) => (
            <MenuItem key={item.key} value={item.key} title={item.title}>
              {item.label}
            </MenuItem>
          ))}
        </Select>
      </FormControl>
    </Box>
  )
}
