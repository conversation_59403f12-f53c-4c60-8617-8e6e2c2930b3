import { <PERSON>, Button } from "@mui/material"
import { memo, useCallback, useMemo } from "react"

import { DEFAULT_ORDER, SEARCH_STR_LENGTH } from "../../../config/game.conf"
import { useIsMobileStore } from "../../../store/useIsMobileStore"
import { EventWizzardFilter, GameFilterSwitch } from "../../../types/types"
import { SEARCH_MODAL_NAME, useModalStore } from "../../modals"

import { type ICTagCat } from "./components/ChipList"
import { Filter } from "./components/Filter"
import { Order, type OrderDirection } from "./components/Order"
import { PlayerCountFilter } from "./components/PlayerCountFilter"
import { type ICSearchBoxTag, SearchBox } from "./components/SearchBox"
import * as styles from "./gameSearch.module.css"

export interface SearchParams {
  search?: string | undefined
  orderBy?: string | undefined
  page?: number | undefined
  order?: string | undefined
  minPlayers?: number | undefined
  maxPlayers?: number | undefined
  playerLevel?: number | undefined
  filter?: EventWizzardFilter | undefined
}

export interface GameSearchProps {
  tags?: ICSearchBoxTag[]
  tagCategories?: ICTagCat[]
  onNavigate: (search: SearchParams) => void
  search: SearchParams
  personalOrder?: boolean
  modal?: boolean
  enableFilters?: GameFilterSwitch
}

export const GameSearch = memo(
  ({
    tags,
    tagCategories,
    onNavigate,
    search,
    personalOrder = false,
    modal = false,
    enableFilters,
  }: GameSearchProps) => {
    const sizeThresholdList = useIsMobileStore(
      (state) => state.sizeThresholdList,
    )

    const { openModal } = useModalStore()

    const doNavigate = useCallback(
      (actions: Partial<typeof search>) => {
        const searchString =
          actions.search !== undefined
            ? actions.search.length >= SEARCH_STR_LENGTH
              ? actions.search
              : ""
            : search.search

        const newSearch: typeof search = {
          page: actions.search ? 1 : search.page,
          search: searchString,
          filter: enableFilters ? (actions.filter ?? search.filter) : undefined,
          order: actions.order ?? search.order,
          orderBy: actions.search
            ? "search"
            : (actions.orderBy ?? search.orderBy),
        }

        if (
          (actions.minPlayers || search.minPlayers) &&
          actions.minPlayers !== -1
        ) {
          newSearch.minPlayers = actions.minPlayers ?? search.minPlayers
          newSearch.playerLevel = search.playerLevel ?? 0
        }

        if (
          (actions.maxPlayers || search.maxPlayers) &&
          actions.maxPlayers !== -1
        ) {
          newSearch.maxPlayers = actions.maxPlayers ?? search.maxPlayers
          newSearch.playerLevel = search.playerLevel ?? 0
        }

        if (actions.playerLevel !== undefined) {
          newSearch.playerLevel = actions.playerLevel
        }

        onNavigate(newSearch)
      },
      [search],
    )

    const onChangeFilter = (filter: EventWizzardFilter | undefined) =>
      doNavigate({ filter })

    const onChange = (orderBy: string, order: OrderDirection) =>
      doNavigate({ orderBy, order })

    const onChangeMin = (min: number) => doNavigate({ minPlayers: min })

    const onChangeMax = (max: number) => doNavigate({ maxPlayers: max })

    const onReset = () =>
      doNavigate({ maxPlayers: 0, minPlayers: 0, playerLevel: 0 })

    const onSetPlayerLevel = (value: number) => {
      doNavigate({ playerLevel: value })
    }

    const onSearch = (search: string) => doNavigate({ search })

    const onSwap = () =>
      doNavigate({
        order: (search.order ?? DEFAULT_ORDER) === "asc" ? "desc" : "asc",
      })

    const filterAndCount = useMemo(
      () => (
        <>
          <Order
            orderBy={search.orderBy}
            onChange={onChange}
            personalOrder={personalOrder}
            onSwap={onSwap}
            order={search.order}
          />
          <Filter
            enableFilters={enableFilters}
            onChange={onChangeFilter}
            filter={search.filter}
          />
          <PlayerCountFilter
            onChangeMax={onChangeMax}
            onChangeMin={onChangeMin}
            playerLevel={search.playerLevel}
            onSetPlayerLevel={onSetPlayerLevel}
            maxPlayers={search.maxPlayers}
            minPlayers={search.minPlayers}
            onReset={onReset}
          />
        </>
      ),
      [
        search.orderBy,
        search.order,
        search.minPlayers,
        search.maxPlayers,
        search.playerLevel,
        search.filter,
      ],
    )

    const openAll = !sizeThresholdList.smallDesktop || modal

    if (openAll) {
      return (
        <Box className={styles.container}>
          <SearchBox
            onSearch={onSearch}
            search={search.search}
            tags={tags}
            tagCategories={tagCategories}
          />
          {filterAndCount}
        </Box>
      )
    }

    return (
      <Box className={styles.containerMobile}>
        <SearchBox
          onSearch={onSearch}
          search={search.search}
          tags={tags}
          tagCategories={tagCategories}
        />
        <Button
          className={styles.moreButton}
          variant="outlined"
          onClick={() =>
            openModal(SEARCH_MODAL_NAME, {
              enableFilters,
            })
          }
        >
          Sort & Filter
        </Button>
      </Box>
    )
  },
)
