.container {
  min-width: 250px;
  height: 250px;
    width: 250px;
    position: relative;
}
.cardContainer {
    width: 100%;
    height: 100%;
}

.card {
    width: 250px;
    min-width: 250px;
    background-position: center;
    background-repeat: no-repeat;
    position: relative;
}

.pointer {
  cursor: pointer;
}

.imageContainer {
  display: flex;
  align-items: center;
  justify-content: center;
}

.image {
  width: 250px;
  height: 250px;
  object-fit: contain;
}

.cardContent {
  background-color: rgba(256, 256, 256, 0.8);
  position: absolute;
  bottom: 0;
  padding-bottom: var(--spacing-7) !important;;
  width: 100%;
  box-sizing: border-box;
}

.newGame {
  position: absolute;
  top: 0;
  left: 0;
}

.bggLink {
  position: absolute;
  top: 0;
  right: 0;
}

.bggRating {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  pointer-events: none;
}

.bubbles {
  position: absolute;
  cursor: default;
  background-color: #fff;
  border: 1px solid;
top: var(--spacing-1);
left: var(--spacing-1);
  vertical-align: middle;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  opacity: 0.6;
}

.lastPlay {
  composes: bubbles;
  top: 90px;
  width: 30px;
  height: 30px;
}

.userRating {
  composes: bubbles;
  width: 40px;
  height: 40px;
}

.playCount {
  composes: bubbles;
  top: 54px;
  left: 8px;
  width: 30px;
  height: 30px;
}

.bubbles:hover {
  opacity: 1;
}

.base {
  box-shadow:
    0 2px 4px -1px rgba(0, 0, 0, 0.2),
    0 4px 5px 0 rgba(110, 168, 192, 0.9),
    0 1px 10px 0 rgba(0, 0, 0, 0.12);
}

.baseInfo {
  position: absolute;
  bottom: var(--spacing-h);
  right: var(--spacing-1);
  display: flex;
  align-items: center;
  justify-content: center;
  pointer-events: none;
}

.overlayContainer {
    width: 100%;
    height: 100%;
    position: absolute;
    pointer-events: none;
    top: 0;
    left: 0;
}

.userList {
    position: absolute;
    bottom: var(--spacing-1);
    left: var(--spacing-2);
    width: 100%;
    pointer-events: none;
}
