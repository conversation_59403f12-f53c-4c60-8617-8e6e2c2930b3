import { EventGameStatus } from "../../../types/types"

import type { ICAvatarUser } from "../../user/UserAvatar/UserAvatar"
import type { LinkComponentProps } from "@tanstack/react-router"

export interface ICThumbnailGame {
  id: number
  title: string
  bggId: number
  rating?: number | null
  average?: number | null
  playCount?: number | null
  lastPlay?: string | null
  eventStatus?: EventGameStatus | null
  canRequest?: boolean | null
  share?: boolean | null
  userOwns?: boolean | null
  userEvent?: number | null
  otherUserStatus?:
    | {
        eventStatus: EventGameStatus | null
        name: string
        avatar: string | null
        color: string | null
        id: number
      }[]
    | null
}

export type ICNavigationProps = Pick<
  LinkComponentProps,
  "to" | "search" | "params"
>

export type ThumbnailUser = ICAvatarUser & {
  eventStatus?: EventGameStatus | null
}
