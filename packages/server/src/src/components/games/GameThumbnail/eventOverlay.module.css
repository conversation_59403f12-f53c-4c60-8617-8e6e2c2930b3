.container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: all;
    z-index: 1000;
}

.eventStatus {
    width: 26px;
    height: 26px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    padding: 2px;
    background-color: rgba(255, 255, 255, 0.8);
    position: absolute;
    top: var(--spacing-16);
    left: var(--spacing-1);
    pointer-events: auto;
    cursor: pointer;
    border: 1px solid;
}

.otherUserStatus {
    background-color: rgba(255, 255, 255, 0.8);
    border-radius: var(--spacing-1);
    padding: 2px;
    position: absolute;
    top: var(--spacing-6);
    right: 2px;
    pointer-events: auto;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-h);
    flex-direction: column;
}

.status {
    opacity: 0.1;
}

.highlighted {
    opacity: 1;
}

.statusButtons {
    position: absolute;
    bottom: var(--spacing-2);
    left: 50%;
    transform: translate(-50%, 0);
    pointer-events: auto;
    background-color: rgba(255, 255, 255, 0.9);
    border-radius: var(--spacing-1);
    padding: var(--spacing-2);
    display: none;
}

.container:hover .statusButtons {
    display: block;
}
