import { Box, Icon } from "@mui/material"
import classnames from "classnames"

import { userGamaStateStrings } from "../../../config/userGameStateStrings"
import { EventGameStatus, EventGameStatusReset } from "../../../types/types"
import { GameStatusButtons } from "../../events/GameStatusButtons/GameStatusButtons"

import * as styles from "./eventOverlay.module.css"

import type { ICThumbnailGame, ThumbnailUser } from "./type"

type EventOverlayProps = {
  game: ICThumbnailGame
  userList?: ThumbnailUser[] | null
  onEventStatusChange?: (status: EventGameStatusReset, gameId: number) => void
}

export const EventOverlay = ({
  onEventStatusChange = (status: EventGameStatusReset, gameId: number) => ({
    status,
    gameId,
  }),
  game,
  userList = [],
}: EventOverlayProps) => {
  const handleClick = (status: EventGameStatusReset) => {
    onEventStatusChange?.(status, game.id)
  }

  const sortedUsers =
    userList && userList.length > 0
      ? userList.reduce<Record<string, typeof userList>>((acc, user) => {
          const key = user.eventStatus
          if (!key) {
            return acc
          }
          if (!acc[key]) acc[key] = []
          acc[key].push(user)
          return acc
        }, {})
      : {
          wish: [],
          willbring: [],
          willplay: [],
          canask: [],
          willnotbring: [],
          maybeplay: [],
        }

  let myEventStatus: EventGameStatus | null = null

  if (game.eventStatus) {
    myEventStatus =
      game.eventStatus ?? (game.canRequest ? "canask" : "willnotbring")
  }

  if (!game) {
    return null
  }

  return (
    <Box className={styles.container}>
      {game.eventStatus && (
        <Box
          className={styles.eventStatus}
          title={userGamaStateStrings[game.eventStatus].titleMy}
        >
          <Icon>{userGamaStateStrings[game.eventStatus].icon}</Icon>
        </Box>
      )}
      {(userList ?? []).length > 0 && (
        <Box className={styles.otherUserStatus} title="Other users status">
          <Icon
            className={classnames(styles.status, {
              [styles.highlighted]: sortedUsers.willbring?.length > 0,
            })}
            title={userGamaStateStrings["willbring"].title}
          >
            {userGamaStateStrings["willbring"].icon}
          </Icon>
          <Icon
            className={classnames(styles.status, {
              [styles.highlighted]:
                sortedUsers.wish?.length > 0 ||
                sortedUsers.willplay?.length > 0 ||
                sortedUsers.maybeplay?.length > 0,
            })}
            title={userGamaStateStrings["willplay"].title}
          >
            {userGamaStateStrings["willplay"].icon}
          </Icon>
          <Icon
            title={userGamaStateStrings["canask"].title}
            className={classnames(styles.status, {
              [styles.highlighted]: sortedUsers.canask?.length > 0,
            })}
          >
            {userGamaStateStrings["canask"].icon}
          </Icon>
        </Box>
      )}
      {game?.share && (
        <Box className={styles.statusButtons}>
          <GameStatusButtons
            small
            onClick={handleClick}
            status={myEventStatus}
            own={!!game.userOwns}
          />
        </Box>
      )}
    </Box>
  )
}
