.container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: all;
    z-index: 1000;
}

.eventStatus {
    width: 26px;
    height: 26px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    padding: 2px;
    background-color: #fff;
    border: 1px solid;
    position: absolute;
    top: 34px;
    left: 68px;
    pointer-events: auto;
}

.otherUserStatus {
    background-color: rgba(255, 255, 255, 0.8);
    border-radius: var(--spacing-1);
    padding: 2px;
    position: absolute;
    top: 1px;
    height: calc(100% - 2px);
    box-sizing: border-box;
    right: 2px;
    pointer-events: auto;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-h);
    flex-direction: column;
}

.status {
    opacity: 0.1;
}

.highlighted {
    opacity: 1;
}

.statusButtons {
    position: absolute;
    bottom: 2px;
    right: 30px;
    pointer-events: auto;
    background-color: rgba(255, 255, 255, 0.9);
    border-radius: var(--spacing-1);
    display: block;
}

.container:hover .statusButtons {
    display: block;
}
