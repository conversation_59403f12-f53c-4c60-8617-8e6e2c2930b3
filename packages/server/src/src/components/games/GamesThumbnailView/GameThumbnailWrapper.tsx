import { useMemo } from "react"

import { useGameStore } from "../../../store/useGamesStore"
import {
  CollectionType,
  EventGameStatus,
  EventGameStatusReset,
  GameUserTuple,
} from "../../../types/types"
import { isNewEntry } from "../../../utils/transformTime"
import { type ICFAvatarOnClick } from "../../user/UserAvatar/UserAvatar"
import { GameListItem } from "../GameListItem/GameListItem"
import { GameThumbnail } from "../GameThumbnail/GameThumbnail"
import {
  type ICNavigationProps,
  type ICThumbnailGame,
} from "../GameThumbnail/type"

export type ICUserThumbnailWrapperGame = ICThumbnailGame & {
  news?: string | null
  users?: GameUserTuple[]
}

interface GameThumbnailProps {
  game: ICUserThumbnailWrapperGame
  communityId?: number
  onUser?: ICFAvatarOnClick
  navigation?: ICNavigationProps
  listMode?: boolean
  type?: CollectionType
  onEventStatusChange?: (status: EventGameStatusReset, gameId: number) => void
}
export const GameThumbnailWrapper = ({
  game,
  communityId,
  onUser,
  navigation,
  type = "community",
  listMode = false,
  onEventStatusChange,
}: GameThumbnailProps) => {
  const { getPopulatedUser } = useGameStore()
  const isNew = game.news ? !isNewEntry(game.news) : false

  const userList = useMemo(() => {
    return game.users
      ? game.users
          .sort((a, b) => (a[1] > b[1] ? -1 : 1))
          .map((user: GameUserTuple) => {
            const userObj = getPopulatedUser(user[0])

            if (!userObj) {
              return undefined
            }

            return {
              ...userObj,
              eventStatus: (user[2] as EventGameStatus) ?? null,
            }
          })
          .filter((user) => user !== undefined)
      : undefined
  }, [game.users, getPopulatedUser])

  const Component = listMode ? GameListItem : GameThumbnail

  return (
    <Component
      type={type}
      navigation={navigation}
      game={game}
      communityId={communityId}
      onUser={onUser}
      userList={userList ?? []}
      onEventStatusChange={onEventStatusChange}
      isNew={isNew}
    />
  )
}
