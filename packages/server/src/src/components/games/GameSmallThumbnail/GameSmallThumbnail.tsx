import { Box, Card, CardContent, Typography } from "@mui/material"
import classnames from "classnames"

import { GAME_IMAGES } from "../../../config/images"
import { PartyLink } from "../../elements/link/PartyLink/PartyLink"
import { BggLink } from "../BggLink/BggLink"

import * as styles from "./gameThumbnail.module.css"
import { ICNavigationProps, ICThumbnailGame } from "./type"

interface GameThumbnailProps {
  game: ICThumbnailGame
  className?: string
  elevation?: number
  navigation?: ICNavigationProps
  onClick?: (id: number) => void
  containerClassName?: string
}
export const GameSmallThumbnail = ({
  game,
  className = "",
  elevation = 1,
  navigation,
  onClick,
  containerClassName = "",
}: GameThumbnailProps) => {
  return (
    <Box className={classnames(styles.container, containerClassName)}>
      <PartyLink
        className={classnames(styles.cardContainer, className, {
          [styles.pointer]: !!navigation,
        })}
        to={navigation?.to}
        search={navigation?.search}
        params={navigation?.params}
        key={game.id}
        title={game.title}
        preload="intent"
        aria-label={`Open ${game.title}`}
        preloadDelay={1000}
        onClick={() => onClick?.(game.id)}
      >
        <Card elevation={elevation} className={styles.card}>
          <Box className={styles.imageContainer}>
            <img
              className={styles.image}
              src={`${ENV_IMAGE_CDN}${GAME_IMAGES}/${game.id}.jpg`}
              loading="lazy"
              alt={`Box art for ${game.title}`}
            />
          </Box>
          <CardContent className={styles.cardContent}>
            <Box>
              <Typography variant="body2" sx={{ fontSize: "0.7rem" }}>
                {game.title}
              </Typography>
            </Box>
          </CardContent>
        </Card>
      </PartyLink>
      <Box className={styles.overlayContainer}>
        <Box className={styles.bggLink}>
          <BggLink
            bggId={game.bggId}
            aria-label={`Open ${game.title} on BGG`}
          />
          {game.average && (
            <Box className={styles.bggRating}>
              <Typography fontWeight={600} aria-label="BGG rating">
                {Math.round(game.average * 10) / 10}
              </Typography>
            </Box>
          )}
        </Box>
      </Box>
    </Box>
  )
}
