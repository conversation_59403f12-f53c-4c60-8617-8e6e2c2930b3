import { create } from "zustand"

export type ModalOptions = Record<string, unknown>

export interface UseModalStoreProps {
  modalList: Record<string, { id: string; isOpen: boolean }>
  createModal: (name: string) => void
  openModal: (name: string, options?: ModalOptions) => void
  closeModal: (name: string) => void
  modalOptions: Record<string, ModalOptions>
  setModalOptions: (name: string, options: ModalOptions) => void
}

export const useModalStore = create<UseModalStoreProps>((set) => ({
  modalList: {},
  modalOptions: {},
  setModalOptions: (name: string, options: ModalOptions) =>
    set((state) => ({
      modalOptions: {
        ...state.modalOptions,
        [name]: options,
      },
    })),
  createModal: (name: string) =>
    set((state) => ({
      modalList: state.modalList[name]
        ? state.modalList
        : { ...state.modalList, [name]: { id: name, isOpen: false } },
    })),
  openModal: (name: string, options: ModalOptions = {}) =>
    set((state) => ({
      modalList: { ...state.modalList, [name]: { id: name, isOpen: true } },
      modalOptions: {
        ...state.modalOptions,
        [name]: options,
      },
    })),
  closeModal: (name: string) =>
    set((state) => ({
      modalList: { ...state.modalList, [name]: { id: name, isOpen: false } },
    })),
}))
