import { create } from "zustand"

import { CollectionType, EventWizzardFilter } from "../types/types"

import type {
  ICommunityGame,
  IGameUser,
  ITag,
  ITagCategory,
} from "../types/tRPC.types"

export type OrderByType = keyof ICommunityGame | "search"

interface GameMeta {
  order: "asc" | "desc"
  orderBy: OrderByType
  search?: string
  minPlayers?: number
  maxPlayers?: number
  playerLevel?: number
  id: number
  type: CollectionType
  filter?: EventWizzardFilter
}

export type GameStoreCollection = {
  meta: GameMeta
  games: ICommunityGame[]
  users: IGameUser[]
  tags: ITag[]
  populatedUsers?: (IGameUser | undefined)[]
  populatedTags?: (ITag | undefined)[][]
  accessId?: number
  tagCategories: ITagCategory[]
}

export interface UseGameStoreProps {
  games: GameStoreCollection[]
  setGames: (newGames: GameStoreCollection) => void
  getGames: (
    type: CollectionType,
    id?: number,
  ) => GameStoreCollection | undefined
  getPopulatedUser: (
    userId: number,
    id?: number,
    type?: CollectionType,
  ) => IGameUser | undefined
  setMeta: (meta: GameMeta) => void
  accessId: number
  revitalizeCollection: (id?: number, type?: CollectionType) => void
  lastAccessedId: number
  lastAccessedType: CollectionType
}

export function isOrderBy(key: string | OrderByType): key is OrderByType {
  return (
    key === "title" ||
    key === "id" ||
    key === "news" ||
    key == "average" ||
    key == "search"
  )
}

export function isOrderByGame(
  key: string | keyof ICommunityGame,
): key is keyof ICommunityGame {
  return isOrderBy(key) && key !== "search"
}

const MAX_STORED_COLLECTIONS = 5

export const useGameStore = create<UseGameStoreProps>((set, get) => ({
  games: [],
  accessId: 0,
  lastAccessedId: 0,
  lastAccessedType: "community",
  setMeta: (meta: GameMeta) =>
    set((state) => {
      const collection = getCollection(state.games, meta.id, meta.type)
      let isDiff = false

      if (!collection) {
        return {}
      }

      Object.keys(meta).forEach((key) => {
        if (
          meta[key as keyof typeof meta] !==
          collection.meta[key as keyof typeof collection.meta]
        ) {
          isDiff = true
        }
      })

      if (!isDiff) {
        return {}
      }

      const updatedCollection = {
        ...collection,
        lastAccessedId: meta.id,
        lastAccessedType: meta.type,
        meta: { ...collection.meta, ...meta },
      }

      return updateAndWrite(state, updatedCollection)
    }),
  setGames: (newGames: GameStoreCollection) =>
    set((state) => {
      return updateAndWrite(state, newGames, true)
    }),
  getGames: (
    type: CollectionType = "community",
    id?: number,
  ): GameStoreCollection | undefined => {
    const useCommunityId = id ?? get().lastAccessedId
    const useType = type ?? get().lastAccessedType
    return getCollection(get().games, useCommunityId, useType)
  },
  getPopulatedUser: (userId: number, id?: number, type?: CollectionType) => {
    const useId = id ?? get().lastAccessedId
    const useType = type ?? get().lastAccessedType
    return getCollection(get().games, useId, useType)?.populatedUsers?.[userId]
  },
  revitalizeCollection: (id?: number, type?: CollectionType) =>
    set((state) => {
      const revitalizeGame = getCollection(
        state.games,
        id ?? state.lastAccessedId,
        type ?? state.lastAccessedType,
      )

      if (!revitalizeGame) {
        return {}
      }

      return updateAndWrite(state, revitalizeGame)
    }),
}))

function getCollection(
  games: GameStoreCollection[],
  id: number,
  type: CollectionType = "community",
): GameStoreCollection | undefined {
  return games.find((game) => {
    return game.meta.id === id && game.meta.type === type
  })
}

function updateAndWrite(
  state: UseGameStoreProps,
  newGame: GameStoreCollection,
  populateEntries: boolean = false,
): Partial<UseGameStoreProps> {
  // remove old game collections from state
  const cleanedGames = state.games.filter((game) => {
    return (
      newGame.meta.id !== game.meta.id &&
      newGame.meta.type !== game.meta.type &&
      (game.accessId ?? 0) > state.accessId - MAX_STORED_COLLECTIONS
    )
  })

  const nextAccessId = state.accessId + 1

  const populatedTags: ITag[][] = []
  const populatedUsers: IGameUser[] = []

  if (populateEntries) {
    newGame.games.forEach((currentGame) => {
      populatedTags[currentGame.id] = currentGame.tags!.map(
        (currentTag) => newGame.tags.find((tag) => tag.id === currentTag)!,
      )
    })

    newGame.users.forEach((user) => {
      populatedUsers[user.id] = user
    })
  }

  return {
    games: [
      ...cleanedGames,
      {
        tags: newGame.tags,
        users: newGame.users,
        tagCategories: newGame.tagCategories,
        populatedUsers: populateEntries
          ? populatedUsers
          : newGame.populatedUsers,
        populatedTags: populateEntries ? populatedTags : newGame.populatedTags,
        games: newGame.games,
        meta: { ...newGame.meta },
        accessId: nextAccessId,
      },
    ],
    lastAccessedId: newGame.meta.id,
    lastAccessedType: newGame.meta.type,
    accessId: nextAccessId,
  }
}
