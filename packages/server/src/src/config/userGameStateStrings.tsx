import BackpackIcon from "@mui/icons-material/Backpack"
import CloseIcon from "@mui/icons-material/Close"
import HighlightOffIcon from "@mui/icons-material/HighlightOff"
import PlayCircleOutlineIcon from "@mui/icons-material/PlayCircleOutline"
import QuestionMarkIcon from "@mui/icons-material/QuestionMark"

import { MaybePlayIcon } from "../../assets/svg"
import { EventGameStatusReset } from "../types/types"

import type { ButtonProps, TypographyProps } from "@mui/material"

export const userGamaStateStrings: {
  [key in EventGameStatusReset]: {
    title: string
    titleMy: string
    color: Extract<ButtonProps["color"], TypographyProps["color"]>
    icon: React.ReactNode
    weight: number
  }
} = {
  reset: {
    title: "Do not want to play",
    titleMy: "Do not want to play",
    color: "inherit",
    icon: <HighlightOffIcon color="inherit" />,
    weight: 6,
  },
  maybeplay: {
    title: "Someone wants to play this game",
    titleMy: "I maybe will play this game",
    color: "inherit",
    icon: <MaybePlayIcon color="inherit" />,
    weight: 4,
  },
  willbring: {
    title: "Someone will bring this game",
    titleMy: "I will bring this game",
    color: "info",
    icon: <BackpackIcon color="info" />,
    weight: 1,
  },
  willplay: {
    title: "Someone wants to play this game",
    titleMy: "I want to play this game",
    color: "success",
    icon: <PlayCircleOutlineIcon color="success" />,
    weight: 2,
  },
  wish: {
    title: "Someone wants to play this game",
    titleMy: "I want to play this game",
    color: "success",
    icon: <PlayCircleOutlineIcon color="success" />,
    weight: 2,
  },
  canask: {
    title: "Someone will bring this game if asked",
    titleMy: "I will bring this game if asked",
    color: "warning",
    icon: <QuestionMarkIcon color="warning" />,
    weight: 3,
  },
  willnotbring: {
    title: "Nobody will bring this game",
    titleMy: "I will not bring this game",
    color: "error",
    icon: <CloseIcon color="error" />,
    weight: 5,
  },
}
