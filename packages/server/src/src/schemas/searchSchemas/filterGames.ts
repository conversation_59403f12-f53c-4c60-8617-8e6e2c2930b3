import { z } from "zod"

import { DEFAULT_ORDER, DEFAULT_ORDER_BY } from "../../config/game.conf"

export const filterGamesSearchSchema = z.object({
  page: z.number().optional().catch(1),
  search: z.string().catch("").optional(),
  order: z.string().catch(DEFAULT_ORDER).optional(),
  orderBy: z.string().catch(DEFAULT_ORDER_BY).optional(),
  minPlayers: z.number().optional(),
  maxPlayers: z.number().optional(),
  playerLevel: z.number().optional(),
  filter: z
    .enum([
      "event-willbring",
      "event-non-cancelled",
      "event-cancelled",
      "event-wishlist",
      "event-canask",
      "none",
    ])
    .optional(),
})

export type FilterGamesSearchSchema = z.infer<typeof filterGamesSearchSchema>
