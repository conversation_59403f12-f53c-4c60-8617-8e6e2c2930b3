import { EventGameStatus } from "../../../../api/src/root/types/game.types"
import { GameUser } from "../components/user/OwnersList/OwnersList"

export type GameUserSort = {
  experience: number | null
} & GameUser

const eventStatusPriority: Record<EventGameStatus, number> = {
  wish: 2,
  willbring: 1,
  canask: 4,
  willnotbring: 5,
  willplay: 2,
  maybeplay: 3,
}

export const ownerSort =
  (myUserId: number) => (t1: GameUserSort, t2: GameUserSort) => {
    if (t1.eventStatus || t2.eventStatus) {
      if (!t1.eventStatus) return -1
      if (!t2.eventStatus) return 1
      const p1 = eventStatusPriority[t1.eventStatus]
      const p2 = eventStatusPriority[t2.eventStatus]
      if (p1 < p2) return -1
      if (p1 > p2) return 1
    }

    if (t1.id === myUserId) return -1
    if (t2.id === myUserId) return 1

    const p1 = t1.experience ?? 0
    const p2 = t2.experience ?? 0

    if (p1 > p2) return -1
    if (p1 < p2) return 1
    if (p1 === p2) {
      return t1.name < t2.name ? -1 : 1
    }

    return 0
  }
