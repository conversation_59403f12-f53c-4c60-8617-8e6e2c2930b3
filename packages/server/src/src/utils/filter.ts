import Fuse from "fuse.js"

import { type ICUserThumbnailWrapperGame } from "../components/games/GamesThumbnailView/GameThumbnailWrapper"
import {
  DEFAULT_ORDER,
  DEFAULT_ORDER_BY,
  SEARCH_STR_LENGTH,
} from "../config/game.conf"
import { type FilterGamesSearchSchema } from "../schemas"
import { type OrderByType } from "../store/useGamesStore"
import { EventWizzardFilter } from "../types/types"

export type IUTFilterGame = ICUserThumbnailWrapperGame & {
  weight: number
  players: {
    box: { min: number; max: number }
    stats?: number[][] | undefined
  }
}

export interface IUTFilterTag {
  title: string
}

export interface IUTUser {
  name: string
}

export function isOrderBy(key: string | OrderByType): key is OrderByType {
  return (
    key === "title" ||
    key === "id" ||
    key === "news" ||
    key === "average" ||
    key === "rating" ||
    key === "weight" ||
    key === "search" ||
    key === "lastPlay" ||
    key === "playCount"
  )
}
export function isOrderByGame(
  key: string | keyof IUTFilterGame,
): key is keyof IUTFilterGame {
  return isOrderBy(key) && key !== "search"
}

type AdditionalOptions = {
  canAsk?: boolean
}

const filterByPlayerCount = (
  game: IUTFilterGame,
  search: FilterGamesSearchSchema,
  level: number,
): boolean => {
  if (level < 3) {
    if (level === 0) {
      return (
        game.players.stats?.reduce((isBest, player) => {
          if (player[0] === search.minPlayers && player[1] < 3) {
            return true
          }

          return isBest
        }, false) ?? false
      )
    }

    return (
      game.players.stats?.reduce((isBest, player) => {
        if (
          player[0] >= (search.minPlayers ?? 0) &&
          player[0] <= (search.maxPlayers ?? 10000000) &&
          player[1] <= level
        ) {
          return true
        }

        return isBest
      }, false) ?? false
    )
  }

  return (
    game.players.box.min <= (search.minPlayers ?? 0) &&
    game.players.box.max >= (search.maxPlayers ?? 10000000)
  )
}

export const applyFilters = (
  games: IUTFilterGame[],
  search: FilterGamesSearchSchema,
  tags?: (IUTFilterTag | undefined)[][],
  users?: (IUTUser | undefined)[],
  defaultFilter?: EventWizzardFilter,
  additionalOptions?: AdditionalOptions,
): IUTFilterGame[] => {
  let gamesResult = games

  if (search?.search && (search.search.length ?? 0 >= SEARCH_STR_LENGTH)) {
    const filterOptionKeys = []

    filterOptionKeys.push({
      name: "title",
      getFn: (game: IUTFilterGame) => {
        return game.title
      },
      weight: 0.7,
    })

    if (users) {
      filterOptionKeys.push({
        name: "name",
        weight: 0.3,
        getFn: (game: IUTFilterGame) =>
          game.users !== undefined
            ? game.users.map((user) => users?.[user[0]]?.name ?? "")
            : "",
      })
    }

    if (tags) {
      filterOptionKeys.push({
        name: "tags",
        weight: 0.5,
        getFn: (game: IUTFilterGame) =>
          tags !== undefined
            ? tags[game.id].map(
                (tag: IUTFilterTag | undefined) => tag?.title ?? "",
              )
            : "",
      })
    }

    const options = {
      minMatchCharLength: SEARCH_STR_LENGTH,
      threshold: 0.3,
      findAllMatches: true,
      includeScore: true,
      useExtendedSearch: true,
      keys: filterOptionKeys,
    }

    const fuseSearch = new Fuse(games, options)

    const searchProps = search.search
      .split(" ")
      .filter((s) => s.length >= SEARCH_STR_LENGTH)
      .join(" | ")

    const result = fuseSearch.search(searchProps)

    gamesResult = result.map((result) => {
      return result.item
    })
  }

  const filter = search.filter ?? defaultFilter

  if (filter && filter !== "none") {
    gamesResult = gamesResult.filter((game) => {
      const map: Record<EventWizzardFilter, string> = {
        "event-willbring": "willbring",
        "event-non-cancelled": "willbring|canask|wish|willplay|maybeplay",
        "event-cancelled": "willnotbring",
        "event-wishlist": "wish|willplay|maybeplay",
        "event-canask": "canask",
        none: "",
      }

      if (!(game.eventStatus === undefined || game.eventStatus === null)) {
        return game.eventStatus?.match(new RegExp(map[filter]))
      }

      if (game.users) {
        return game.users.some((gameUser) => {
          return (gameUser[2] ?? "").match(new RegExp(map[filter]))
        })
      }

      if (!additionalOptions) {
        return false
      }

      if (
        additionalOptions.canAsk &&
        (filter === "event-canask" || filter === "event-non-cancelled")
      ) {
        return true
      }

      return !additionalOptions.canAsk && filter === "event-cancelled"
    })
  }

  if (
    search.minPlayers ||
    (search.maxPlayers && (search.playerLevel ?? 0) > 0)
  ) {
    const level = search.playerLevel ?? 3 // 3 - box, 2 - recomended, 1 - best, 0 - exact (only compare min, only if is value and recomended or best)
    gamesResult = gamesResult.filter((game) =>
      filterByPlayerCount(game, search, level),
    )
  }

  const useOrderBy = search.orderBy ?? DEFAULT_ORDER_BY
  const useOrder = search.order ?? DEFAULT_ORDER

  if (isOrderByGame(useOrderBy)) {
    gamesResult = gamesResult.sort((game1, game2) => {
      const order = useOrder === "asc" ? 1 : -1

      const game1Value =
        useOrderBy === "lastPlay" || useOrderBy === "news"
          ? new Date(game1[useOrderBy] ?? 0)
          : game1[useOrderBy]
      const game2Value =
        useOrderBy === "lastPlay" || useOrderBy === "news"
          ? new Date(game2[useOrderBy] ?? 0)
          : game2[useOrderBy]

      if (!game1Value) {
        return -1 * order
      }

      if (!game2Value) {
        return 1 * order
      }

      if (game1Value > game2Value) {
        return 1 * order
      }

      if (game1Value < game2Value) {
        return -1 * order
      }

      return 0
    })
  }

  return gamesResult
}
