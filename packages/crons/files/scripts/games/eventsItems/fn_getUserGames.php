<?php
function getUserGames($database, $relatedUser, $eventId)
{
    $userPossibleBaseGames = $database->query("
    SELECT
        `user2base_game`.`game_id`,
        `will_teach`,
        `play_priority`,
        `user2game2event`.`status`
    FROM 
        `user2base_game`
    INNER JOIN `games` ON `user2base_game`.`game_id` = `games`.`id`
    LEFT JOIN `user2game2event` ON (
        `user2game2event`.`game_id` = `games`.`id`
        AND `user2game2event`.`user_id` = " . $relatedUser['user_id'] . "
        AND `user2game2event`.`event_id` = " . $eventId . "
    )
    WHERE
        `user2base_game`.`user_id` = " . $relatedUser['user_id'] . "
    AND 
        `deleted` = '0'
    AND (
        `user2game2event`.`status` != 'willnotbring'
        " . ($relatedUser['can_request'] == 1 ? "OR `user2game2event`.`status` IS NULL" : "AND `user2game2event`.`status` IS NOT NULL") . "
    )
    ")->fetchALl();

    $gameItemList = [];

    foreach ($userPossibleBaseGames as $game) {
        $expansions = $database->query("
        SELECT
            `expansion_id` as `game_id`
        FROM 
            `game2expansion`
        WHERE
            `game_id` = " . $game['game_id'] . "
        ")->fetchALl();

        $gameItemList = array_merge($gameItemList, $expansions);
    }

    return $gameItemList;
}