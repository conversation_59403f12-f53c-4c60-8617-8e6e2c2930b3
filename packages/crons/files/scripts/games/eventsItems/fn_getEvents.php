<?php
function getEvents($database, $max, $interval)
{
    return $database->query("
        SELECT 
            `id`
        FROM 
            `events`
        WHERE 
            (`last_updated` < '" . date("Y-m-d H:i:s", time() - ($interval * 60)) . "' OR `last_updated` IS NULL)
            and (`state` = 'open' OR `state` = 'ongoing' OR `state` = 'hidden')
        ORDER BY `last_updated` ASC
        LIMIT " . $max)->fetchALl();
}