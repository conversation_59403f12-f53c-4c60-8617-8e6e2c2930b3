<?php
function getTagList($database, $gameId, $settings)
{
    return $database->query("
    SELECT
        `tag_id`,
        `type_id`
    FROM 
        `game2tag`
    INNER JOIN `tags` ON `game2tag`.`tag_id` = `tags`.`id`
    WHERE
         `type_id` NOT IN  (" . findSetting($settings['settings'], "tags:auto_length_tag_type_id") . ", " . findSetting($settings['settings'], "tags:auto_weight_tag_type_id") . ")
    AND
        `game_id` = " . $gameId . "
    AND
        `visible` = '1'
    ")->fetchALl();
}