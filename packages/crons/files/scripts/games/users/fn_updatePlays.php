<?php

function updatePlays($database, $list, $userId)
{
    foreach ($list as $play) {
        if (!$play['item'] || !isset($play['item']['@attributes']) || !isset($play['item']['@attributes']['objectid'])) {
            continue;
        }
        
        $game = $database->get('games', ['id',
            'bgg_id'], ['bgg_id' => intval($play['item']['@attributes']['objectid'])]);

        if ($game) {
            $select_game_link = $database->get('user2game', ['id',
                'cal_last_play'], ['user_id' => $userId,
                'game_id' => $game['id']]);

            if (!empty($select_game_link)) {
                $newTime = strtotime($play['@attributes']['date']);
                $existingTime = $select_game_link['cal_last_play'] === null ? 0 : strtotime($select_game_link['cal_last_play']);

                if ($newTime > $existingTime) {
                    $newPlay['cal_last_play'] = $play['@attributes']['date'];
                    $database->update('user2game', $newPlay, ['user_id' => $userId, 'game_id' => $game['id']]);
                }
            }
        }
    }
}