<?php

function updateGameList($database, $user, $settings)
{
    $updateTime = date("Y-m-d H:i:s", time());

    $database->insert('user_update_chart', ['user_id' => $user['id'],
        'status' => 1,
        'ran' => $updateTime]);

    $latestUpdateId = $database->id();

    try {
        $database->action(function ($database) use (&$user, &$updateTime, &$latestUpdateId, &$settings) {

            $xml_link = 'https://www.boardgamegeek.com/xmlapi2/collection?username=' . $user['bgg_username'] . '&stats=1&own=1';
            $doc = RequestFile::run($xml_link, findSetting($settings['settings'], "cron:user_init_call_sleep"));

            if (!$doc) {
                throw new Exception('Failed to get XML');
            }

            cleanUpDeleted($database, $doc, $user);

            extractGames($database, $doc, $user);

            $database->update('users', ['last_updated' => $updateTime], ['id' => $user['id']]);

            if (!isset($_GET['run'])) {
                throw new Exception('Just stop!!!');
            }

            $database->update("user_update_chart", ['status' => 2], ['id' => $latestUpdateId]);
        });
    } catch (Exception $e) {
        $database->update("user_update_chart", ['status' => 3], ['id' => $latestUpdateId]);
        echo '<pre>' . var_dump($database->log()) . '</pre>';
        echo "Failed with: ", $e->getMessage(), "\n";
    }
}