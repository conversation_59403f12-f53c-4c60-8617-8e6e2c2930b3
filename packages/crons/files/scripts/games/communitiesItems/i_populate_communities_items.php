<?php
require __DIR__ . "/../common.php";
require "fn_getCommunities.php";
require "fn_updateCommunity.php";
require "fn_getUserGames.php";
require "fn_stripBlanks.php";
require "fn_getTagList.php";
require "fn_saveGameData.php";

function subImport($database, $SesClient)
{
    $settings = getSettings($database, "community_items");

    $communities = getCommunities($database, $settings['crons']['items_per_batch'], $settings['crons']['refetch'] * 60);

    foreach ($communities as $community) {
        updateCommunity($database, $community, $settings);
    }

    echo "Job done!";
}



