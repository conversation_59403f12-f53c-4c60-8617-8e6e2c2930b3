<?php
require __DIR__ . "/../common.php";
require "fn_getExpansionList.php";
require "fn_getUserGames.php";
require "fn_getUserExperienceScore.php";
require "fn_fillUser2Base.php";
require "fn_getEvents.php";
require "fn_updateEvent.php";
require "fn_stripBlanks.php";
require "fn_saveGameData.php";

function subImport($database, $SesClient)
{
    $settings = getSettings($database, "event_games");

    $events = getEvents($database, $settings['crons']['items_per_batch'], $settings['crons']['refetch'] * 60);
    
    foreach ($events as $event) {
        updateEvent($database, $event, $settings);
    }

    echo "Job done!";
}



