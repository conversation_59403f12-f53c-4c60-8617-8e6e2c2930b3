<?php

function updateEvent($database, $event, $settings)
{
    // get all community users that allow sharing games
    $relatedUsers = $database->query("
    SELECT
        `permission_user2role`.`user_id`,
        `user2event`.`can_request`
    FROM 
        `permission_user2role`
    INNER JOIN 
            `user2event` ON `user2event`.`user_id` = `permission_user2role`.`user_id`
        AND `user2event`.`event_id` = " . $event['id'] . "
    WHERE
        `subject_id` = " . $event['id'] . "
    AND
        `permission_user2role`.`role_id` IN ('participant')
    AND
        `permission_user2role`.`subject` = 'event' 
    AND 
        `user2event`.`share_games` = 1
    AND 
        `user2event`.`wizzard_state` = 'done'
    ")->fetchALl();

    $userGames = [];

    // get all games for each user
    foreach ($relatedUsers as $relatedUser) {
        $userGames[$relatedUser['user_id']] = getUserGames($database, $relatedUser, $event['id']);
    }

    // create game list with all users for game from this community
    $gameList = [];
    foreach ($userGames as $user => $userGameData) {
        foreach ($userGameData as $gameData) {
            if (!isset($gameList[$gameData['game_id']])) {
                $gameList[$gameData['game_id']] = [];
            }

            $gameList[$gameData['game_id']][$user] = $gameData;
        }
    }

    // save all data for all things
    foreach ($gameList as $gameId => $gameData) {
        $expansions = getExpansionList($database, $gameId, $event['id'], $settings);
        saveGameData($database, $gameData, $gameId, $event['id'], $expansions, $settings);
    }

    // remove all entries with no users
    stripBlanks($database, $gameList, $event['id']);
}