<?php

function getUserExperienceScore($gameData, &$expansionUserGameData, $settings)
{
    $scoreMarkers = ['lastPlay' => 3,
        'willTeach' => 0,
        'anytime' => 5,
        'often' => 3,
        'on-request' => 1,
        'never' => -1,
        'please-burn-the-game' => -10,
        'expansions' => 1,
        'news' => 5];

    foreach ($gameData as $userId => $metadata) {
        $expansionUserGameData[$userId]['metadata'] = $metadata;
    }

    $newUserData = [];

    foreach ($expansionUserGameData as $userId => $userData) {
        if (!isset($userData['last_play'])) {
            continue;
        }

        $score = 0;
        if ($userData['last_play'] > time() - findSetting($settings['settings'], 'games:played_before')) {
            $score = $scoreMarkers['lastPlay'];
        }

        if (isset($userData['metadata'])) {
            if (isset($scoreMarkers[$userData['metadata']['play_priority']])) {
                $score = $score + $scoreMarkers[$userData['metadata']['play_priority']];
            }

            if ($userData['metadata']['will_teach'] == 1) {
                $score = $score + $scoreMarkers['willTeach'];
            }
        }

        if ($userData['rating'] > 0) {
            $avgRating = ceil($userData['rating'] / $userData['rating_count']);

            $score = $score + ($avgRating - 5);
        }

        if ($userData['play_count'] > 0) {
            // we don't want 100 plays give 100 points. Less points - bigger the impact
            // if player has 100+ plays - each next play will be even less impactful
            // from experimenting with graphs - seems that 7 is required to normalize data so formula is: y=\sqrt{\sqrt{x}}+7
            $score = $score + (($userData['play_count'] > 100) ? ceil(sqrt(sqrt($userData['play_count'])) + 7) : ceil(sqrt($userData['play_count'])));
        }

        $update = false;
        if ($userData['news'] > time() - (findSetting($settings['settings'], 'games:news_before') * 60 * 60 * 24)) {
            $score = $score + $userData['news'];
            $update = true;
        }

        $score = $score + ceil(sqrt($userData['exp_count']));

        $expansionUserGameData[$userId]['score'] = $score;
        $newUserData[$userId] = ['score' => $score, 'news' => $update];
    }

    return $newUserData;
}