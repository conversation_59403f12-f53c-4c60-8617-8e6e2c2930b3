<?php
function fillUser2Base($database, $expansionUserGameData, $gameId)
{
    foreach ($expansionUserGameData as $userId => $userData) {
        if (!isset($userData['last_play'])) {
            continue;
        }
        
        $newData = [];
        if ($userData['last_play'] > 0) {
            $newData['last_play'] = date("Y-m-d H:i:s", $userData['last_play']);
        }

        if ($userData['play_count'] > 0) {
            $newData['play_count'] = $userData['play_count'];
        }

        if ($userData['base_rating'] > 0) {
            $newData['rating'] = $userData['base_rating'];
        }

        if ($userData['score'] > 0) {
            $newData['experience'] = $userData['score'];
        }

        if (!empty($newData)) {
            $database->update('user2base_game', $newData, ['user_id' => $userId, 'game_id' => $gameId]);
        }
    }
}