<?php
set_time_limit(0);

function getCurl($url, $database, $identifier, $skipMaintenance)
{
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.116 Safari/537.36');
    curl_setopt($ch, CURLOPT_HTTPHEADER, array('Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',));
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 0);
    curl_setopt($ch, CURLOPT_TIMEOUT, 60 * 60 * 1); //timeout in seconds

    $response = curl_exec($ch);
    $httpcode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    var_dump($httpcode);
    var_dump($response);

    logRunDetails($database, $identifier, "response", $skipMaintenance, $response);
    logRunDetails($database, $identifier, "response", $skipMaintenance, $httpcode);

    if ($httpcode >= 400 || $httpcode == 202) {
        return false;
    }

    return true;
}

function logRunDetails($db, $identifier, $type, $skipMaintenance, $details = "")
{
    $maintenance = ["start", "nothing-to-do", "end", "done"];
    // 'start','run','nothing-to-do','done','failed','other','end'

    if (!$skipMaintenance && in_array($type, $maintenance)) {
        return;
    }

    $db->insert("log_cron_runs", array("identifier" => $identifier, "type" => $type, "details" => $details));
}

function subImport($database, $SesClient): void
{
    $identifier = time() . rand(10000, 99999);

    $settings = getSettings($database);

    $skipMaintenance = findSetting($settings['settings'], "cron:skip_maintenance") === 1;

    logRunDetails($database, $identifier, "start", $skipMaintenance);

    $canRunBlocking = true;
    $runScripts = [];
    $runBlocking = null;
    $recentBlockingRun = null;

    $sec = 60; // should be 60 when not testing!!!

    foreach ($settings['crons'] as $cron) {
        $timestamp = strtotime($cron['last_run']);
        // var_dump("Time:", time(), "Timestamp:", $timestamp, "Period:", $cron['run_period'] * $sec, "If is:", $timestamp <= time() - ($cron['run_period'] * $sec), "Current diff:", $timestamp - (time() - ($cron['run_period'] * $sec)));
        // echo "\n\r";
        if ($timestamp <= time() - ($cron['run_period'] * $sec) && $cron['is_on'] === 1) {
            if ($cron['blocking'] == 1) {
                if ($cron['is_running'] === 1) {
                    $canRunBlocking = false;
                }

                if ($canRunBlocking && !$runBlocking && ($recentBlockingRun === null || $recentBlockingRun > $timestamp)) {
                    $runBlocking = $cron;
                    $recentBlockingRun = $timestamp;
                }
            }

            if ($cron['blocking'] == 0 && $cron['is_running'] === 0) {
                $runScripts[] = $cron;
            }
        }
    }

    if ($canRunBlocking && $runBlocking !== null) {
        $runScripts[] = $runBlocking;
    }

    if (count($runScripts) === 0) {
        logRunDetails($database, $identifier, "nothing-to-do", $skipMaintenance);
        logRunDetails($database, $identifier, "end", $skipMaintenance);
        return;
    }

    $cron = $runScripts[0];

    $when = date("Y-m-d H:i:s");
    logRunDetails($database, $identifier, "run", $skipMaintenance, json_encode(['script' => $cron['id'], 'when' => $when]));

    $database->update("crons", ['is_running' => 1, 'last_run' => $when], ['id' => $cron['id']]);

    switch ($cron['id']) {
        case 'community_games':
            $action = "communities_games";
            break;
        case 'community_items':
            $action = "communities_items";
        case 'event_games':
            $action = "event_games";
            break;
        case 'event_items':
            $action = "event_items";
            break;
        case 'users':
            $action = "users";
            break;
        case 'reset_broken':
            $action = "reset";
            break;
        case 'new_games':
            $action = "newgames";
            break;
        case 'existing_games':
            $action = "games";
            break;
        case 'base_games':
            $action = "base_games";
            break;
        default:
            return;
    }

    $re = getCurl('http://pr-crons/?do=games&action=' . $action . '&run=1&status=1', $database, $identifier, $skipMaintenance);

    if ($re) {
        logRunDetails($database, $identifier, "done", $skipMaintenance);
    } else {
        logRunDetails($database, $identifier, "failed", $skipMaintenance);
    }

    $database->update("crons", ['is_running' => 0, 'last_run' => date("Y-m-d H:i:s")], ['id' => $cron['id']]);
    logRunDetails($database, $identifier, "end", $skipMaintenance);
}