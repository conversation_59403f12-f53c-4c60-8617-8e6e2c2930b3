-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: pr-db-mysql:3306
-- Generation Time: Sep 10, 2025 at 02:38 PM
-- Server version: 5.7.24
-- PHP Version: 8.2.8

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `party`
--

-- --------------------------------------------------------

--
-- Table structure for table `agr_game2community2`
--

CREATE TABLE `agr_game2community2` (
                                       `game_id` int(11) NOT NULL,
                                       `community_id` int(11) NOT NULL,
                                       `user_count` smallint(5) UNSIGNED NOT NULL,
                                       `last_updated` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                                       `agr_user_data` text NOT NULL,
                                       `agr_expansion_data` text NOT NULL,
                                       `agr_tag_data` text NOT NULL,
                                       `news` timestamp NOT NULL DEFAULT '0000-00-00 00:00:00'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- --------------------------------------------------------

--
-- Table structure for table `agr_game2event`
--

CREATE TABLE `agr_game2event` (
                                  `id` int(11) NOT NULL,
                                  `game_id` int(11) NOT NULL,
                                  `event_id` int(11) NOT NULL,
                                  `last_updated` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Last time when game entry was validated',
                                  `created` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                                  `agr_user_data` text,
                                  `agr_tag_data` text,
                                  `last_changes` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Last time when something was changed in agr fields',
                                  `agr_expansion_data` text NOT NULL,
                                  `user_count` int(11) NOT NULL DEFAULT '0',
                                  `news` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- --------------------------------------------------------

--
-- Table structure for table `agr_item2community`
--

CREATE TABLE `agr_item2community` (
                                      `game_id` int(11) NOT NULL,
                                      `community_id` int(11) NOT NULL,
                                      `last_updated` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                                      `news` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                                      `agr_users` text NOT NULL,
                                      `agr_tags` text NOT NULL,
                                      `agr_expansion_tags` text NOT NULL,
                                      `user_count` int(11) NOT NULL DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- --------------------------------------------------------

--
-- Table structure for table `agr_item2event`
--

CREATE TABLE `agr_item2event` (
                                  `game_id` int(11) NOT NULL,
                                  `event_id` int(11) NOT NULL,
                                  `last_updated` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                                  `news` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                                  `agr_users` text NOT NULL,
                                  `agr_tags` text NOT NULL,
                                  `agr_expansion_tags` text NOT NULL,
                                  `user_count` int(11) NOT NULL DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- --------------------------------------------------------

--
-- Table structure for table `community`
--

CREATE TABLE `community` (
                             `id` int(11) NOT NULL,
                             `name` varchar(256) NOT NULL,
                             `openness` enum('public','publicLimited','private','closed') NOT NULL DEFAULT 'private',
                             `created` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                             `member_approval` tinyint(4) NOT NULL DEFAULT '1',
                             `last_updated` timestamp NULL DEFAULT NULL,
                             `allow_share` tinyint(1) NOT NULL DEFAULT '1',
                             `online` varchar(256) DEFAULT NULL,
                             `location` varchar(256) DEFAULT NULL,
                             `description` text NOT NULL,
                             `image` varchar(256) NOT NULL,
                             `welcome` text,
                             `events` enum('all','trusted','moders','none') NOT NULL DEFAULT 'moders'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Table structure for table `community2event`
--

CREATE TABLE `community2event` (
                                   `community_id` int(11) NOT NULL,
                                   `event_id` int(11) NOT NULL,
                                   `owner` tinyint(4) NOT NULL DEFAULT '1',
                                   `created` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                                   `invite` tinyint(4) NOT NULL DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- --------------------------------------------------------

--
-- Table structure for table `community_invites`
--

CREATE TABLE `community_invites` (
                                     `id` int(11) NOT NULL,
                                     `community_id` int(11) NOT NULL,
                                     `invite_string` varchar(30) NOT NULL,
                                     `user_id` int(11) DEFAULT NULL,
                                     `expiration` timestamp NULL DEFAULT NULL,
                                     `status` enum('sent','used','expired','cancelled') NOT NULL DEFAULT 'sent',
                                     `accept_limit` int(11) DEFAULT NULL,
                                     `count` int(11) NOT NULL DEFAULT '0',
                                     `accepted` timestamp NULL DEFAULT NULL,
                                     `inviter_id` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- --------------------------------------------------------

--
-- Table structure for table `crons`
--

CREATE TABLE `crons` (
                         `id` varchar(20) NOT NULL,
                         `last_run` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                         `is_on` tinyint(4) NOT NULL DEFAULT '0',
                         `refetch` int(11) NOT NULL COMMENT 'refetch period in minutes',
                         `run_period` int(11) NOT NULL DEFAULT '10' COMMENT 'script run interval',
                         `is_running` tinyint(4) NOT NULL DEFAULT '0',
                         `blocking` int(11) NOT NULL DEFAULT '1',
                         `items_per_batch` int(11) NOT NULL DEFAULT '100'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- --------------------------------------------------------

--
-- Table structure for table `events`
--

CREATE TABLE `events` (
                          `id` int(11) NOT NULL,
                          `title` int(11) NOT NULL,
                          `created` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                          `starts` timestamp NULL DEFAULT NULL,
                          `state` enum('open','ongoing','ended','hidden','cancelled') NOT NULL DEFAULT 'hidden',
                          `ends` timestamp NULL DEFAULT NULL,
                          `member_approval` tinyint(4) NOT NULL DEFAULT '1',
                          `openness` enum('public','publicLimited','community','communitySeniority','private','closed') NOT NULL DEFAULT 'private',
                          `max_capacity` int(11) DEFAULT NULL,
                          `reserve_capacity` int(11) DEFAULT NULL,
                          `location` varchar(256) NOT NULL,
                          `description` text NOT NULL,
                          `going` int(11) NOT NULL COMMENT 'How many people are going',
                          `reserve` int(11) DEFAULT NULL COMMENT 'How many people are in reserve',
                          `allow_share` tinyint(4) NOT NULL DEFAULT '0',
                          `min_capacity` int(11) NOT NULL DEFAULT '0',
                          `small_description` text,
                          `has_age_limit` tinyint(4) NOT NULL DEFAULT '0',
                          `image` varchar(256) DEFAULT NULL,
                          `lat` float DEFAULT NULL,
                          `lng` float DEFAULT NULL,
                          `allow_infinite_organizers` int(11) NOT NULL DEFAULT '0' COMMENT 'This is NOT meant to be editable by host, but only by admins. If true - will remove fixed amount of max Communities and cohosts.\r\n',
                          `creator_id` int(11) DEFAULT NULL,
                          `share_type` enum('all','hosts','single-game','none','host-managed') NOT NULL DEFAULT 'all',
                          `last_updated` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Table structure for table `event_list_preset`
--

CREATE TABLE `event_list_preset` (
                                     `id` int(11) NOT NULL,
                                     `user_id` int(11) NOT NULL,
                                     `title` varchar(30) NOT NULL,
                                     `agr_games` text NOT NULL,
                                     `created` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- --------------------------------------------------------

--
-- Table structure for table `event_request_comments`
--

CREATE TABLE `event_request_comments` (
                                          `game_id` int(11) NOT NULL,
                                          `user_id` int(11) DEFAULT NULL,
                                          `message` text NOT NULL,
                                          `id` int(11) NOT NULL,
                                          `created` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                                          `deleted` tinyint(4) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Table structure for table `game2expansion`
--

CREATE TABLE `game2expansion` (
                                  `expansion_id` int(11) NOT NULL,
                                  `game_id` int(11) NOT NULL,
                                  `visible` tinyint(4) NOT NULL DEFAULT '1',
                                  `player_count_sum` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'if player count different than base game - type of addition. 1 - will treat as single increase. 0 - ignore; 2 - will treat as cumulative for each expansion. ENd calc is Max(Max(1), Sum(2))'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Table structure for table `game2tag`
--

CREATE TABLE `game2tag` (
                            `game_id` int(11) NOT NULL,
                            `tag_id` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- --------------------------------------------------------

--
-- Table structure for table `games`
--

CREATE TABLE `games` (
                         `id` int(11) NOT NULL,
                         `title` varchar(256) NOT NULL,
                         `bgg_id` int(11) DEFAULT NULL,
                         `created` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                         `type` enum('new','base','base-expansion','expansion','accessory','other') NOT NULL DEFAULT 'base',
                         `bgg_info` text,
                         `last_updated` timestamp NULL DEFAULT NULL,
                         `description` text
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Table structure for table `log_cron_runs`
--

CREATE TABLE `log_cron_runs` (
                                 `id` int(11) NOT NULL,
                                 `created` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                                 `type` enum('start','run','nothing-to-do','done','failed','other','end','response') NOT NULL,
                                 `details` mediumtext NOT NULL,
                                 `identifier` bigint(20) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- --------------------------------------------------------

--
-- Table structure for table `messages`
--

CREATE TABLE `messages` (
                            `id` int(11) NOT NULL,
                            `user_from_id` int(11) DEFAULT NULL,
                            `user_to_id` int(11) DEFAULT NULL,
                            `message` text,
                            `from_deleted` tinyint(4) NOT NULL DEFAULT '0',
                            `received` timestamp NULL DEFAULT NULL,
                            `created` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                            `flagged` tinyint(4) NOT NULL DEFAULT '0',
                            `to_deleted` tinyint(4) NOT NULL DEFAULT '0',
                            `from_deletion_time` timestamp NULL DEFAULT NULL,
                            `to_deletion_time` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Table structure for table `permission_roles`
--

CREATE TABLE `permission_roles` (
                                    `role` varchar(20) NOT NULL,
                                    `title` varchar(20) NOT NULL,
                                    `subject` enum('global','community','userdata','event') NOT NULL DEFAULT 'global'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- --------------------------------------------------------

--
-- Table structure for table `permission_user2role`
--

CREATE TABLE `permission_user2role` (
                                        `user_id` int(11) NOT NULL,
                                        `role_id` varchar(20) NOT NULL,
                                        `subject` enum('global','community','userdata','event') NOT NULL DEFAULT 'global',
                                        `subject_id` int(11) DEFAULT NULL,
                                        `id` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- --------------------------------------------------------

--
-- Table structure for table `role_settings`
--

CREATE TABLE `role_settings` (
                                 `id` int(11) NOT NULL,
                                 `role_id` varchar(20) NOT NULL,
                                 `setting_name` enum('create_communities_max') NOT NULL,
                                 `value` varchar(100) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- --------------------------------------------------------

--
-- Table structure for table `settings`
--

CREATE TABLE `settings` (
                            `name` varchar(40) NOT NULL,
                            `description` text NOT NULL,
                            `value` varchar(50) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- --------------------------------------------------------

--
-- Table structure for table `tags`
--

CREATE TABLE `tags` (
                        `id` int(11) NOT NULL,
                        `bgg_id` int(11) DEFAULT NULL,
                        `title` varchar(256) NOT NULL,
                        `created` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                        `visible` tinyint(4) NOT NULL DEFAULT '0',
                        `category` enum('mechanic','category','family','auto','custom') NOT NULL DEFAULT 'mechanic',
                        `display_title` varchar(30) NOT NULL,
                        `type` varchar(15) NOT NULL,
                        `type_id` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- --------------------------------------------------------

--
-- Table structure for table `tag_merge`
--

CREATE TABLE `tag_merge` (
                             `tag_from_id` int(11) NOT NULL,
                             `tag_to_id` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- --------------------------------------------------------

--
-- Table structure for table `tag_types`
--

CREATE TABLE `tag_types` (
                             `id` int(11) NOT NULL,
                             `name` varchar(20) NOT NULL,
                             `color` varchar(6) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- --------------------------------------------------------

--
-- Table structure for table `user2base_game`
--

CREATE TABLE `user2base_game` (
                                  `user_id` int(11) NOT NULL,
                                  `game_id` int(11) NOT NULL,
                                  `last_updated` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
                                  `experience` int(11) NOT NULL,
                                  `last_play` timestamp NULL DEFAULT NULL,
                                  `play_count` int(11) NOT NULL,
                                  `rating` float DEFAULT NULL,
                                  `portability` enum('home','anywhere','onrequest','nowhere') DEFAULT NULL,
                                  `events` enum('willbring','canask','willnotbring') DEFAULT NULL,
                                  `play_priority` enum('anytime','often','on-request','on-plead','never','please-burn-the-game') DEFAULT NULL,
                                  `updated_last` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                                  `will_teach` tinyint(4) DEFAULT NULL,
                                  `deleted` int(11) NOT NULL DEFAULT '0',
                                  `calc_tags` text NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- --------------------------------------------------------

--
-- Table structure for table `user2community`
--

CREATE TABLE `user2community` (
                                  `user_id` int(11) NOT NULL,
                                  `community_id` int(11) NOT NULL,
                                  `created` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                                  `trust` int(11) NOT NULL DEFAULT '0',
                                  `shareMyGames` tinyint(4) NOT NULL DEFAULT '1'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Table structure for table `user2event`
--

CREATE TABLE `user2event` (
                              `user_id` int(11) NOT NULL,
                              `event_id` int(11) NOT NULL,
                              `wizzard_state` enum('skip','settings','pick-games','review-games','done','waiting') NOT NULL DEFAULT 'waiting',
                              `created` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                              `can_request` tinyint(4) NOT NULL DEFAULT '1',
                              `share_games` tinyint(4) NOT NULL DEFAULT '1'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Table structure for table `user2game`
--

CREATE TABLE `user2game` (
                             `user_id` int(11) NOT NULL,
                             `game_id` int(11) NOT NULL,
                             `cal_last_play` timestamp NULL DEFAULT NULL,
                             `cal_play_count` int(11) NOT NULL DEFAULT '0',
                             `bgg_collid` bigint(20) DEFAULT NULL COMMENT 'id for unique user 2 game entry from bgg',
                             `deleted` tinyint(4) NOT NULL DEFAULT '0',
                             `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '1 - owned, 2 - wishlist',
                             `rating` float DEFAULT NULL,
                             `bgg_data` text,
                             `id` int(11) NOT NULL,
                             `news` timestamp NOT NULL DEFAULT '0000-00-00 00:00:00'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Table structure for table `user2game2event`
--

CREATE TABLE `user2game2event` (
                                   `user_id` int(11) NOT NULL,
                                   `game_id` int(11) NOT NULL,
                                   `event_id` int(11) NOT NULL,
                                   `status` enum('wish','willbring','canask','willnotbring','willplay') DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Table structure for table `users`
--

CREATE TABLE `users` (
                         `id` int(11) NOT NULL,
                         `name` varchar(255) NOT NULL,
                         `created` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                         `email` varchar(255) DEFAULT NULL,
                         `status` enum('banned','unverified','user','moder','admin','superadmin') NOT NULL DEFAULT 'unverified',
                         `trust` int(11) NOT NULL DEFAULT '0',
                         `active` tinyint(1) NOT NULL DEFAULT '1',
                         `bgg_username` varchar(256) DEFAULT NULL,
                         `last_updated` datetime DEFAULT NULL,
                         `auth_id` varchar(40) NOT NULL,
                         `avatar` varchar(255) DEFAULT NULL,
                         `color` varchar(6) DEFAULT NULL,
                         `last_play_count` int(11) NOT NULL DEFAULT '0',
                         `total_games` int(11) NOT NULL DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Table structure for table `user_update_chart`
--

CREATE TABLE `user_update_chart` (
                                     `id` int(11) NOT NULL,
                                     `user_id` int(11) NOT NULL,
                                     `ran` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                                     `status` tinyint(4) NOT NULL DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

--
-- Indexes for dumped tables
--

--
-- Indexes for table `agr_game2community2`
--
ALTER TABLE `agr_game2community2`
    ADD PRIMARY KEY (`game_id`,`community_id`) USING BTREE,
    ADD KEY `community_id` (`community_id`);

--
-- Indexes for table `agr_game2event`
--
ALTER TABLE `agr_game2event`
    ADD PRIMARY KEY (`id`),
    ADD KEY `game_id` (`game_id`),
    ADD KEY `event_id` (`event_id`);

--
-- Indexes for table `agr_item2community`
--
ALTER TABLE `agr_item2community`
    ADD PRIMARY KEY (`game_id`,`community_id`),
    ADD KEY `community_id` (`community_id`);

--
-- Indexes for table `agr_item2event`
--
ALTER TABLE `agr_item2event`
    ADD PRIMARY KEY (`event_id`,`game_id`) USING BTREE,
    ADD KEY `game_id` (`game_id`);

--
-- Indexes for table `community`
--
ALTER TABLE `community`
    ADD PRIMARY KEY (`id`),
    ADD KEY `openness` (`openness`);

--
-- Indexes for table `community2event`
--
ALTER TABLE `community2event`
    ADD KEY `community_id` (`community_id`),
    ADD KEY `event_id` (`event_id`),
    ADD KEY `community_id_2` (`community_id`,`event_id`);

--
-- Indexes for table `community_invites`
--
ALTER TABLE `community_invites`
    ADD PRIMARY KEY (`id`),
    ADD KEY `user_id` (`user_id`),
    ADD KEY `inviter_id` (`inviter_id`,`community_id`) USING BTREE,
    ADD KEY `invite_string` (`invite_string`,`expiration`) USING BTREE;

--
-- Indexes for table `crons`
--
ALTER TABLE `crons`
    ADD PRIMARY KEY (`id`);

--
-- Indexes for table `events`
--
ALTER TABLE `events`
    ADD PRIMARY KEY (`id`);

--
-- Indexes for table `event_list_preset`
--
ALTER TABLE `event_list_preset`
    ADD PRIMARY KEY (`id`);

--
-- Indexes for table `event_request_comments`
--
ALTER TABLE `event_request_comments`
    ADD PRIMARY KEY (`id`) USING BTREE,
    ADD KEY `id` (`game_id`) USING BTREE,
    ADD KEY `user_id` (`user_id`);

--
-- Indexes for table `game2expansion`
--
ALTER TABLE `game2expansion`
    ADD PRIMARY KEY (`expansion_id`,`game_id`),
    ADD KEY `game_id` (`game_id`,`visible`) USING BTREE;

--
-- Indexes for table `game2tag`
--
ALTER TABLE `game2tag`
    ADD PRIMARY KEY (`game_id`,`tag_id`),
    ADD KEY `tag_id` (`tag_id`);

--
-- Indexes for table `games`
--
ALTER TABLE `games`
    ADD PRIMARY KEY (`id`),
    ADD UNIQUE KEY `bgg_id` (`bgg_id`) USING BTREE,
    ADD KEY `last_updated` (`last_updated`),
    ADD KEY `type` (`type`);

--
-- Indexes for table `log_cron_runs`
--
ALTER TABLE `log_cron_runs`
    ADD PRIMARY KEY (`id`);

--
-- Indexes for table `messages`
--
ALTER TABLE `messages`
    ADD PRIMARY KEY (`id`),
    ADD KEY `user_from_id` (`user_from_id`),
    ADD KEY `user_to_id` (`user_to_id`);

--
-- Indexes for table `permission_roles`
--
ALTER TABLE `permission_roles`
    ADD PRIMARY KEY (`role`);

--
-- Indexes for table `permission_user2role`
--
ALTER TABLE `permission_user2role`
    ADD PRIMARY KEY (`id`),
    ADD KEY `user_id` (`user_id`,`role_id`,`subject_id`),
    ADD KEY `role_id` (`role_id`);

--
-- Indexes for table `role_settings`
--
ALTER TABLE `role_settings`
    ADD PRIMARY KEY (`id`),
    ADD KEY `role_id` (`role_id`);

--
-- Indexes for table `settings`
--
ALTER TABLE `settings`
    ADD PRIMARY KEY (`name`);

--
-- Indexes for table `tags`
--
ALTER TABLE `tags`
    ADD PRIMARY KEY (`id`),
    ADD UNIQUE KEY `bgg_id` (`bgg_id`) USING BTREE,
    ADD KEY `visible` (`visible`),
    ADD KEY `tags_ibfk_1` (`type_id`,`visible`) USING BTREE;

--
-- Indexes for table `tag_merge`
--
ALTER TABLE `tag_merge`
    ADD KEY `tag_from_id` (`tag_from_id`),
    ADD KEY `tag_to_id` (`tag_to_id`);

--
-- Indexes for table `tag_types`
--
ALTER TABLE `tag_types`
    ADD PRIMARY KEY (`id`);

--
-- Indexes for table `user2base_game`
--
ALTER TABLE `user2base_game`
    ADD PRIMARY KEY (`user_id`,`game_id`),
    ADD KEY `last_updated` (`last_updated`),
    ADD KEY `game_id` (`game_id`),
    ADD KEY `user_id` (`user_id`,`deleted`,`last_updated`) USING BTREE;

--
-- Indexes for table `user2community`
--
ALTER TABLE `user2community`
    ADD PRIMARY KEY (`user_id`,`community_id`),
    ADD KEY `community_id` (`community_id`);

--
-- Indexes for table `user2event`
--
ALTER TABLE `user2event`
    ADD PRIMARY KEY (`user_id`,`event_id`),
    ADD KEY `event_id` (`event_id`);

--
-- Indexes for table `user2game`
--
ALTER TABLE `user2game`
    ADD PRIMARY KEY (`id`),
    ADD UNIQUE KEY `bgg_collid` (`bgg_collid`) USING BTREE,
    ADD KEY `user_id` (`user_id`,`deleted`) USING BTREE,
    ADD KEY `game_id` (`game_id`) USING BTREE;

--
-- Indexes for table `user2game2event`
--
ALTER TABLE `user2game2event`
    ADD PRIMARY KEY (`user_id`,`game_id`,`event_id`),
    ADD KEY `event_id` (`event_id`),
    ADD KEY `game_id` (`game_id`);

--
-- Indexes for table `users`
--
ALTER TABLE `users`
    ADD PRIMARY KEY (`id`),
    ADD UNIQUE KEY `id` (`id`),
    ADD UNIQUE KEY `auth0_id` (`auth_id`),
    ADD UNIQUE KEY `users_email` (`email`),
    ADD KEY `bgg_username` (`last_updated`,`bgg_username`) USING BTREE;

--
-- Indexes for table `user_update_chart`
--
ALTER TABLE `user_update_chart`
    ADD PRIMARY KEY (`id`),
    ADD KEY `user_id` (`user_id`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `agr_game2event`
--
ALTER TABLE `agr_game2event`
    MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `community`
--
ALTER TABLE `community`
    MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `community_invites`
--
ALTER TABLE `community_invites`
    MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `events`
--
ALTER TABLE `events`
    MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `event_list_preset`
--
ALTER TABLE `event_list_preset`
    MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `event_request_comments`
--
ALTER TABLE `event_request_comments`
    MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `games`
--
ALTER TABLE `games`
    MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `log_cron_runs`
--
ALTER TABLE `log_cron_runs`
    MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `messages`
--
ALTER TABLE `messages`
    MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `permission_user2role`
--
ALTER TABLE `permission_user2role`
    MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `role_settings`
--
ALTER TABLE `role_settings`
    MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `tags`
--
ALTER TABLE `tags`
    MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `tag_types`
--
ALTER TABLE `tag_types`
    MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `user2game`
--
ALTER TABLE `user2game`
    MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `users`
--
ALTER TABLE `users`
    MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `user_update_chart`
--
ALTER TABLE `user_update_chart`
    MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `agr_game2community2`
--
ALTER TABLE `agr_game2community2`
    ADD CONSTRAINT `agr_game2community2_ibfk_1` FOREIGN KEY (`community_id`) REFERENCES `community` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    ADD CONSTRAINT `agr_game2community2_ibfk_2` FOREIGN KEY (`game_id`) REFERENCES `games` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Constraints for table `agr_game2event`
--
ALTER TABLE `agr_game2event`
    ADD CONSTRAINT `agr_game2event_ibfk_1` FOREIGN KEY (`game_id`) REFERENCES `games` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    ADD CONSTRAINT `agr_game2event_ibfk_2` FOREIGN KEY (`event_id`) REFERENCES `events` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Constraints for table `agr_item2community`
--
ALTER TABLE `agr_item2community`
    ADD CONSTRAINT `agr_item2community_ibfk_1` FOREIGN KEY (`community_id`) REFERENCES `community` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    ADD CONSTRAINT `agr_item2community_ibfk_2` FOREIGN KEY (`game_id`) REFERENCES `games` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Constraints for table `agr_item2event`
--
ALTER TABLE `agr_item2event`
    ADD CONSTRAINT `agr_item2event_ibfk_1` FOREIGN KEY (`event_id`) REFERENCES `events` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    ADD CONSTRAINT `agr_item2event_ibfk_2` FOREIGN KEY (`game_id`) REFERENCES `games` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Constraints for table `community2event`
--
ALTER TABLE `community2event`
    ADD CONSTRAINT `community2event_ibfk_1` FOREIGN KEY (`community_id`) REFERENCES `community` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    ADD CONSTRAINT `community2event_ibfk_2` FOREIGN KEY (`event_id`) REFERENCES `events` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Constraints for table `community_invites`
--
ALTER TABLE `community_invites`
    ADD CONSTRAINT `community_invites_ibfk_1` FOREIGN KEY (`inviter_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    ADD CONSTRAINT `community_invites_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE SET NULL;

--
-- Constraints for table `event_request_comments`
--
ALTER TABLE `event_request_comments`
    ADD CONSTRAINT `event_request_comments_ibfk_1` FOREIGN KEY (`game_id`) REFERENCES `games` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    ADD CONSTRAINT `event_request_comments_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE SET NULL;

--
-- Constraints for table `game2expansion`
--
ALTER TABLE `game2expansion`
    ADD CONSTRAINT `game2expansion_ibfk_1` FOREIGN KEY (`expansion_id`) REFERENCES `games` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    ADD CONSTRAINT `game2expansion_ibfk_2` FOREIGN KEY (`game_id`) REFERENCES `games` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Constraints for table `game2tag`
--
ALTER TABLE `game2tag`
    ADD CONSTRAINT `game2tag_ibfk_1` FOREIGN KEY (`game_id`) REFERENCES `games` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    ADD CONSTRAINT `game2tag_ibfk_2` FOREIGN KEY (`tag_id`) REFERENCES `tags` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Constraints for table `messages`
--
ALTER TABLE `messages`
    ADD CONSTRAINT `messages_ibfk_1` FOREIGN KEY (`user_from_id`) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE SET NULL,
    ADD CONSTRAINT `messages_ibfk_2` FOREIGN KEY (`user_to_id`) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE SET NULL;

--
-- Constraints for table `permission_user2role`
--
ALTER TABLE `permission_user2role`
    ADD CONSTRAINT `permission_user2role_ibfk_1` FOREIGN KEY (`role_id`) REFERENCES `permission_roles` (`role`) ON DELETE CASCADE ON UPDATE CASCADE,
    ADD CONSTRAINT `permission_user2role_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Constraints for table `role_settings`
--
ALTER TABLE `role_settings`
    ADD CONSTRAINT `role_settings_ibfk_1` FOREIGN KEY (`role_id`) REFERENCES `permission_roles` (`role`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Constraints for table `tags`
--
ALTER TABLE `tags`
    ADD CONSTRAINT `tags_ibfk_1` FOREIGN KEY (`type_id`) REFERENCES `tag_types` (`id`) ON DELETE SET NULL ON UPDATE CASCADE;

--
-- Constraints for table `tag_merge`
--
ALTER TABLE `tag_merge`
    ADD CONSTRAINT `tag_merge_ibfk_2` FOREIGN KEY (`tag_from_id`) REFERENCES `tags` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    ADD CONSTRAINT `tag_merge_ibfk_3` FOREIGN KEY (`tag_to_id`) REFERENCES `tags` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Constraints for table `user2base_game`
--
ALTER TABLE `user2base_game`
    ADD CONSTRAINT `user2base_game_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    ADD CONSTRAINT `user2base_game_ibfk_2` FOREIGN KEY (`game_id`) REFERENCES `games` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Constraints for table `user2community`
--
ALTER TABLE `user2community`
    ADD CONSTRAINT `user2community_ibfk_1` FOREIGN KEY (`community_id`) REFERENCES `community` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    ADD CONSTRAINT `user2community_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Constraints for table `user2event`
--
ALTER TABLE `user2event`
    ADD CONSTRAINT `user2event_ibfk_1` FOREIGN KEY (`event_id`) REFERENCES `events` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    ADD CONSTRAINT `user2event_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Constraints for table `user2game`
--
ALTER TABLE `user2game`
    ADD CONSTRAINT `user2game_ibfk_1` FOREIGN KEY (`game_id`) REFERENCES `games` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    ADD CONSTRAINT `user2game_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Constraints for table `user2game2event`
--
ALTER TABLE `user2game2event`
    ADD CONSTRAINT `user2game2event_ibfk_1` FOREIGN KEY (`event_id`) REFERENCES `events` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    ADD CONSTRAINT `user2game2event_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    ADD CONSTRAINT `user2game2event_ibfk_3` FOREIGN KEY (`game_id`) REFERENCES `games` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Constraints for table `user_update_chart`
--
ALTER TABLE `user_update_chart`
    ADD CONSTRAINT `user_update_chart_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;